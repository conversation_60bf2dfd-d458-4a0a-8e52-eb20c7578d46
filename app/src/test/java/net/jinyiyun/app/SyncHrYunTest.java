/**
 * <AUTHOR>
 * @DATE 2023/8/24
 */
package net.jinyiyun.app;

import cn.hutool.core.collection.CollectionUtil;
import com.mashape.unirest.http.exceptions.UnirestException;
import net.jinyiyun.framework.dto.OrderDeliveryDetailDto;
import net.jinyiyun.framework.entity.GiveGoods;
import net.jinyiyun.framework.entity.GoodsSpec;
import net.jinyiyun.framework.entity.Order;
import net.jinyiyun.framework.entity.Partner;
import net.jinyiyun.framework.partner.service.PartnerMainService;
import net.jinyiyun.framework.partner.service.model.BO.PartnerBO;
import net.jinyiyun.framework.query.OrderDeliveryDetailQuery;
import net.jinyiyun.framework.service.*;
import net.jinyiyun.framework.service.hryun.HrYunService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/24
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class SyncHrYunTest {

    @Autowired
    private HrYunService hrYunService;

    @Autowired
    private GoodsSpecService goodsSpecService;

    @Autowired
    private GiveGoodsService giveGoodsService;

    @Autowired
    private PartnerService partnerService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderDeliveryDetailService orderDeliveryDetailService;

    @Autowired
    private PartnerMainService partnerMainService;

    /**
     * 商品同步 821条数据
     */
    @Test
    public void test1() throws InterruptedException {
        List<GoodsSpec> list = goodsSpecService.list();
        ArrayList<Long> success = new ArrayList<>();
        ArrayList<Long> fail = new ArrayList<>();

        for (GoodsSpec goodsSpec : list) {
            try {
                hrYunService.syncGoodsSpec(goodsSpec, null, null, 1, "主品");
                success.add(goodsSpec.getId());

            } catch (UnirestException e) {
                fail.add(goodsSpec.getId());
                System.out.println(e.getMessage());
            }
            Thread.sleep(500);
        }
        System.out.println("同步成功的：" + success);
        System.out.println("同步失败的：" + fail);


    }


    /**
     * 赠品 188条
     */
    @Test
    public void test2() throws InterruptedException {
        List<GiveGoods> list = giveGoodsService.list();
        ArrayList<Long> success = new ArrayList<>();
        ArrayList<Long> fail = new ArrayList<>();
        for (GiveGoods giveGoods : list) {
            try {
                hrYunService.syncGoodsSpec(null, giveGoods, null, 2, "赠品");
                success.add(giveGoods.getId());
            } catch (UnirestException e) {
                fail.add(giveGoods.getId());
                System.out.println(e.getMessage());
            }
            Thread.sleep(500);
        }
        System.out.println("同步成功的：" + success);
        System.out.println("同步失败的：" + fail);


    }


    /**
     * 同步订单 4000多条
     */
    @Test
    @Deprecated
    public void test3() throws InterruptedException {
        // 线下渠道的合伙人
        Set<Long> collect = partnerService.lambdaQuery()
                .eq(Partner::getChannelId, 1)
                .list().stream().map(x -> x.getId()).collect(Collectors.toSet());
        ArrayList<Integer> stateList = new ArrayList<>();
        // 待收货，和完成的订单。
        stateList.add(3);
        stateList.add(10);
        List<Order> list = orderService.lambdaQuery()
                .in(Order::getPartnerId, collect)
                .like(Order::getCreatedAt, "%2024-10-%")
                .in(Order::getState, stateList)
                .list();
        ArrayList<Long> success = new ArrayList<>();
        HashMap<Long, String> failMap = new HashMap<>();
        ArrayList<String> failList = new ArrayList<>();
        for (Order order : list) {
            try {
                // 同步海软云
                hrYunService.syncOrder(order);
                success.add(order.getId());
            } catch (Exception e) {
                if (!e.getMessage().contains("第三方系统的订单号已存在")) {
                    failMap.put(order.getId(), e.getMessage());
                }
            }
            Thread.sleep(1500);
        }
        System.out.println("同步成功的：" + success);
        // 格式化打印输出失败的
        if (CollectionUtil.isNotEmpty(failMap)) {
            for (Map.Entry<Long, String> entry : failMap.entrySet()) {
                System.out.println(entry.getKey());
                System.out.println(entry.getValue());
                System.out.println("================================");
            }
        }

    }


    public void calcGoods(String input,List<String> list) {
        // 定义正则表达式模式
        String regex = "【(.*?)】";

        // 创建Pattern对象
        Pattern pattern = Pattern.compile(regex);

        // 创建Matcher对象
        Matcher matcher = pattern.matcher(input);

        // 用于存储结果的变量
        String commodityCode = null;
        String thirdPartyCode = null;

        int count = 0;
        while (matcher.find()) {
            if (count == 0) {
                commodityCode = matcher.group(1);
            } else if (count == 1) {
                thirdPartyCode = matcher.group(1);
                break; // 找到两个值后退出循环
            }
            count++;
        }
        String res = "商品编码: " + commodityCode + "第三方编码: " + thirdPartyCode;
        list.add(res);
    }



    /**
     * 合伙人经销商
     * 440
     */
    @Test
    public void test4() throws InterruptedException {



        // 待同步的,只同步线下的。
        List<Partner> list = partnerService.lambdaQuery()
                .eq(Partner::getChannelId,1)
                .list();
        ArrayList<Long> success = new ArrayList<>();
        ArrayList<Long> fail = new ArrayList<>();
        ArrayList<Long> useracountfail = new ArrayList<>();
        ArrayList<String> otherfail = new ArrayList<>();

        for (Partner partner : list) {
            // 同步
            try {
                //todo tyt
                //hrYunService.syncPartners(partner);
                success.add(partner.getId());
            } catch (Exception e) {
                fail.add(partner.getId());
                if (e.getMessage().contains("用户账号")) {
                    useracountfail.add(partner.getId());
                } else {

                    otherfail.add(e.getMessage());
                }
            }
            Thread.sleep(2000);
        }

        System.out.println("同步成功的："+success);
        System.out.println("同步失败的："+fail);
        System.out.println("同步失败的中用户账号不正确的："+useracountfail);
        System.out.println("同步失败的中其它错误的："+otherfail);


    }


    @Test
    public void syncOrder() {
        // 线下渠道的合伙人
        Set<Long> collect = partnerService.lambdaQuery()
                .eq(Partner::getChannelId, 1)
                .list().stream().map(x -> x.getId()).collect(Collectors.toSet());
        // 待收货，和完成的订单。
        ArrayList<Integer> stateList = new ArrayList<>();
        stateList.add(3);
        stateList.add(10);
        List<Order> list = orderService.lambdaQuery()
                .in(Order::getPartnerId, collect)
                .like(Order::getCreatedAt, "2025-01-")
                .in(Order::getState, stateList)
                .list();
        for (Order order : list) {
            try {
                // 同步海软云
                hrYunService.syncOrder(order);
                Thread.sleep(1500);
            } catch (Exception e) {

            }
        }
    }


    // 下面是单条数据的同步。不用看。


    /**
     * 主品
     */
    @Test
    public void test5() {


        GoodsSpec good = goodsSpecService.getById(1784870345433120770l);
        good.setAttr("你好");
        try {
            hrYunService.syncGoodsSpec(good, null, null, 1, "主品");
        } catch (UnirestException e) {
            e.printStackTrace();
        }


    }


    /**
     * 赠品
     */
    @Test
    public void test6() {
        GiveGoods giveGoods = giveGoodsService.getById(1784865767279001602L);
        giveGoods.setTitle("我的心");

        try {
            hrYunService.syncGoodsSpec(null, giveGoods, null, 2, "赠品");
        } catch (UnirestException e) {
            e.printStackTrace();
        }
    }

    /**
     * 货品的删除
     */
    @Test
    public void test66() {

        hrYunService.delGoodsPec(Arrays.asList(1784865767279001602L));
    }


    /**
     * 订单
     */
    @Test
    public void test7() throws UnirestException {
        Order order = orderService.getById(1866385740664508418L);
        try {
            // 同步海软云
            hrYunService.syncOrder(order);
        } catch (Exception e) {
            System.out.println(e);
        }
    }



    /**
     * 合伙人经销商测试完毕
     */
    @Test
    public void test8() {
        PartnerBO partner = partnerMainService.getById(2L);

        try {
            hrYunService.syncPartners(partner);
        } catch (UnirestException e) {
            System.out.println(e);
        }
    }


    @Test
    public void test9() {
        OrderDeliveryDetailQuery orderDeliveryDetailQuery = new OrderDeliveryDetailQuery();
        orderDeliveryDetailQuery.setPartnerId(1672803799890677761l);
        orderDeliveryDetailQuery.setCreatedAtStart(LocalDateTime.of(2023, 6, 28, 10, 30, 45));
        orderDeliveryDetailQuery.setCreatedAtEnd(LocalDateTime.now());
        List<OrderDeliveryDetailDto> orderDeliveryDetailDtos = orderDeliveryDetailService.selectOrderDeliveryDetails(orderDeliveryDetailQuery);
        System.out.println(orderDeliveryDetailDtos);

    }


    /**
     * 同步指定订单
     */
    @Test
    public void test10() {
        orderService.jkyGetOrderDeliveryByOrderId(1822188188507688962l);
    }


}
