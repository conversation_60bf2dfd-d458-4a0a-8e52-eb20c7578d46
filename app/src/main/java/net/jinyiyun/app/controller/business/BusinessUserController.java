package net.jinyiyun.app.controller.business;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import net.jinyiyun.app.service.BusinessHelpService;
import net.jinyiyun.config.exception.AlertException;
import net.jinyiyun.config.request.PageReq;
import net.jinyiyun.config.response.Resp;
import net.jinyiyun.framework.entity.User;
import net.jinyiyun.framework.service.UserService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 业务员
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/business/user")
@RequiredArgsConstructor
@PreAuthorize("hasAnyAuthority('ROLE_BUSINESS_MANAGER','ROLE_BUSINESS_MEMBER')")
public class BusinessUserController {

    private final UserService userService;
    private final BusinessHelpService businessHelpService;


    @PreAuthorize("hasAuthority('user')")
    @GetMapping
    public Resp index(PageReq pageDto, User dto) {

        QueryWrapper<User> wrapper = businessHelpService.filterPartnerIdQueryWrapper(dto);
        if (wrapper == null) {
            return Resp.success();
        }
        wrapper.eq("type", User.Type.PARTNER.getCode());
        return Resp.success(userService.page(pageDto.toMpPage(), wrapper));
    }


    @PreAuthorize("hasAuthority('user:add')")
    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public Resp store(@RequestBody @Validated User dto) {
        userService.lambdaQuery().eq(User::getTel, dto.getTel()).oneOpt().ifPresent(u -> {
            throw new AlertException("手机号已存在");
        });
        dto.setPassword(new BCryptPasswordEncoder().encode(dto.getPassword()));
        dto.setType(User.Type.PARTNER.getCode());
        userService.save(dto);
        return Resp.success();
    }


    @PreAuthorize("hasAuthority('user:edit')")
    @PatchMapping("{id}")
    @Transactional(rollbackFor = Exception.class)
    public Resp update(@PathVariable Long id, @RequestBody @Validated User dto) {
        dto.setId(id);
        dto.setPassword(StrUtil.isBlank(dto.getPassword()) ? null : new BCryptPasswordEncoder().encode(dto.getPassword()));
        dto.setType(User.Type.PARTNER.getCode());
        userService.updateById(dto);
        return Resp.success();
    }

}
