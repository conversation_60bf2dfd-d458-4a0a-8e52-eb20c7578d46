package net.jinyiyun.app.controller;

import lombok.RequiredArgsConstructor;
import net.jinyiyun.app.service.weixin.mini.WeChatMiniProgramService;
import net.jinyiyun.app.service.weixin.mini.dto.SessionResp;
import net.jinyiyun.app.service.weixin.mini.dto.WeChatMiniProgramPhoneOldReq;
import net.jinyiyun.app.service.weixin.mini.dto.WeChatMiniProgramPhoneReq;
import net.jinyiyun.config.exception.AlertException;
import net.jinyiyun.config.response.Resp;
import net.jinyiyun.framework.entity.User;
import net.jinyiyun.framework.service.SmsRecordService;
import net.jinyiyun.framework.service.UserService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Pattern;
import java.util.Optional;

/**
 * 登录授权
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("auth")
@RequiredArgsConstructor
public class AuthController {

    private final UserService userService;

    private final SmsRecordService smsRecordService;

    private final WeChatMiniProgramService weChatMiniProgramService;


    /**
     * 验证码
     */
    @GetMapping("send/sms")
    public Resp sms(@RequestParam @Pattern(regexp = "^[1][0-9]{10}$", message = "手机号格式不正确") String tel) {
        Optional.ofNullable(userService.getByTel(tel)).orElseThrow(() -> new AlertException("手机号不存在"));
        smsRecordService.aliSendSmsCode(tel, "SMS_255940094");
        return Resp.success();
    }


    /**
     * 账号密码登录 只允许web端口业务员/经理访问
     *
     * @param dto User
     * @return Result
     */
    @PostMapping("login")
    public Resp login(@RequestBody @Validated User dto) {
        User user = Optional.ofNullable(userService.getByTel(dto.getTel())).orElseThrow(() -> new AlertException("手机号不存在或者密码错误"));
        if (User.Type.AUDIT.getCode().equals(user.getType())){
            throw new AlertException("只支持业务员/经理登录");
        }
        return Resp.success(Resp.SUCCESS, userService.loginByPassword(dto));
    }


    /**
     * 手机号验证码登录
     */
    @GetMapping("login/sms")
    public Resp loginSms(@RequestParam @Pattern(regexp = "^[1][0-9]{10}$", message = "手机号格式不正确") String tel,
                         @RequestParam String smsCode) {
        smsRecordService.checkSmsCode(tel, smsCode);
        return Resp.success(Resp.SUCCESS, userService.loginByTel(tel));
    }


    /**
     * 小程序登录 兼容旧版
     */
    @PostMapping("login/wechat/mini/program/old")
    public Resp login(@RequestBody @Validated WeChatMiniProgramPhoneOldReq dto) {
        SessionResp session = weChatMiniProgramService.getSessionKeyByCode(dto.getCode());
        String tel = weChatMiniProgramService.getUserInfo(dto.getEncryptedData(), session.getSessionKey(), dto.getIv());
        return Resp.success(Resp.SUCCESS, userService.loginByTel(tel));
    }


    /**
     * 小程序登录 新版
     */
    @PostMapping("login/wechat/mini/program")
    public Resp login(@RequestBody @Validated WeChatMiniProgramPhoneReq dto) {
        String tel = weChatMiniProgramService.getUserInfo(dto.getECode(), weChatMiniProgramService.getStableAccessToken());
        return Resp.success(Resp.SUCCESS, userService.loginByTel(tel));
    }


    /**
     * 小程序审核账号密码登录
     */
    @PostMapping("audit/login/wechat/mini/program")
    public Resp auditLogin(@RequestBody @Validated User dto){
        User user = Optional.ofNullable(userService.getByTel(dto.getTel())).orElseThrow(() -> new AlertException("手机号不存在或者密码错误"));
        if (!User.Type.AUDIT.getCode().equals(user.getType())){
            throw new AlertException("只支持审核账号登录");
        }
        return Resp.success(Resp.SUCCESS, userService.loginByPassword(dto));
    }


}
