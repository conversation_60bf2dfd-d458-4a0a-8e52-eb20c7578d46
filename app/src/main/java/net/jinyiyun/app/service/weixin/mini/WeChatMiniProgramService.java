package net.jinyiyun.app.service.weixin.mini;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.jinyiyun.app.service.weixin.mini.dto.PhoneInfoBo;
import net.jinyiyun.app.service.weixin.mini.dto.PhoneResp;
import net.jinyiyun.app.service.weixin.mini.dto.SessionResp;
import net.jinyiyun.common.utils.JacksonUtil;
import net.jinyiyun.config.exception.AlertException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.spec.AlgorithmParameterSpec;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信小程序服务
 *
 * <AUTHOR>
 */
@Component
@ConditionalOnBean({Properties.class})
@Slf4j
@RequiredArgsConstructor
public class WeChatMiniProgramService {


    private final Properties properties;


    /**
     * 获取code url
     */
    private static final String URL_CODE = "https://api.weixin.qq.com/sns/jscode2session?appid={}&secret={}&js_code={}&grant_type=authorization_code";

    /**
     * 获取accessToken url
     */
    private static final String URL_ACCESS_TOKEN = "https://api.weixin.qq.com/cgi-bin/stable_token";


    /**
     * 根据code和accessToken获取手机号 url
     */
    private static final String URL_GET_PHONE = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token={}";


    /**
     * 根据code获取session
     *
     * @param code code
     * @return sessionKey
     */
    public SessionResp getSessionKeyByCode(String code) {
        String url = StrUtil.format(URL_CODE, properties.getAppId(), properties.getAppSecret(), code);
        SessionResp codeBo = JacksonUtil.str2obj(HttpUtil.get(url), SessionResp.class);
        if (codeBo == null) {
            throw new AlertException("获取session失败");
        }
        return codeBo;
    }


    /**
     * 包括敏感数据在内的完整用户信息的加密数据
     * 旧版登录
     *
     * @param encryptedData 包括敏感数据在内的完整用户信息的加密数据
     * @param sessionKey    数据进行加密签名的密钥
     * @param ivStr         getUserInfo
     * @return 手机号
     */
    public String getUserInfo(String encryptedData, String sessionKey, String ivStr) {
        String encryptedText;

        PhoneInfoBo phoneInfo = null;
        try {
            // 开始解密
            // 开始解密
            byte[] encData = Base64Utils.decode(encryptedData.getBytes());
            byte[] iv = Base64Utils.decode(ivStr.getBytes());
            byte[] key = Base64Utils.decode(sessionKey.getBytes());

            AlgorithmParameterSpec ivSpec = new IvParameterSpec(iv);
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

            // 解密结果
            byte[] encryptedTextBytes = cipher.doFinal(encData);
            if (encryptedTextBytes == null) {
                throw new AlertException("用户信息解密失败!");
            }
            // 解密结果
            encryptedText = new String(encryptedTextBytes, StandardCharsets.UTF_8);
            phoneInfo = JacksonUtil.str2obj(encryptedText, PhoneInfoBo.class);
        } catch (Exception e) {
            log.error("微信小程序解密失败:{}", e.getMessage());
        }
        if (phoneInfo == null || StrUtil.isBlank(phoneInfo.getPhoneNumber())) {
            throw new AlertException("获取用户信息失败!");
        }
        return phoneInfo.getPhoneNumber();
    }


    /**
     * 包括敏感数据在内的完整用户信息的加密数据
     * 该方式无法获取openId和unionId
     *
     * @param code        code
     * @param accessToken accessToken
     * @return 手机号
     */
    public String getUserInfo(String code, String accessToken) {
        HashMap<String, Object> map = new HashMap<>(1);
        map.put("code", code);
        String resp = HttpUtil.createPost(StrUtil.format(URL_GET_PHONE, accessToken))
                .body(JacksonUtil.obj2str(map))
                .execute()
                .body();

        PhoneResp phoneResp = JacksonUtil.str2obj(resp, PhoneResp.class);
        if (phoneResp == null) {
            throw new AlertException("获取用户信息失败!{}", resp);
        }
        if (!phoneResp.getErrCode().equals(0)) {
            throw new AlertException("获取用户信息失败!{}", phoneResp.getErrMsg());
        }
        if (StrUtil.isBlank(phoneResp.getPhoneInfo().getPhoneNumber())) {
            throw new AlertException("获取用户信息失败!{}", "手机号为空");
        }
        return phoneResp.getPhoneInfo().getPhoneNumber();
    }


    /**
     * 获取accessToken
     * 在当前类中使用缓存无效
     */
    @Cacheable(value = "accessToken", key = "'weChatMiniProgram'", cacheManager = "cacheManagerAfterWrite2Hours")
    public String getStableAccessToken() {
        Map<String, Object> param = new HashMap<>();
        param.put("grant_type", "client_credential");
        param.put("appid", properties.getAppId());
        param.put("secret", properties.getAppSecret());
        param.put("force_refresh", false);
        JSONObject res = JSONUtil.parseObj(HttpUtil.post(URL_ACCESS_TOKEN, JSONUtil.toJsonStr(param)));
        String accessToken = res.getStr("access_token");
        if (StrUtil.isBlank(accessToken)) {
            throw new AlertException("获取accessToken失败!原因:", res.getStr("errmsg", ""));
        }
        return accessToken;
    }

}
