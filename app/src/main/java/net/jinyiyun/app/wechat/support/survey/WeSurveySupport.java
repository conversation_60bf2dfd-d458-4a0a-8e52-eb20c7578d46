package net.jinyiyun.app.wechat.support.survey;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.jinyiyun.app.wechat.support.survey.convert.WeSurveyAnswerConvert;
import net.jinyiyun.app.wechat.support.survey.convert.WeSurveyConvert;
import net.jinyiyun.app.wechat.support.survey.model.VO.WeSurveyUserInfoVO;
import net.jinyiyun.app.wechat.support.survey.model.VO.WeSurveyVO;
import net.jinyiyun.app.wechat.support.survey.model.request.SurveyAnswerAddRequest;
import net.jinyiyun.common.security.Auth;
import net.jinyiyun.config.exception.AlertException;
import net.jinyiyun.framework.common.model.VO.SurveyQuestion;
import net.jinyiyun.framework.entity.Order;
import net.jinyiyun.framework.entity.User;
import net.jinyiyun.framework.partner.service.PartnerMainService;
import net.jinyiyun.framework.partner.service.model.BO.PartnerBO;
import net.jinyiyun.framework.service.AreaService;
import net.jinyiyun.framework.service.OrderService;
import net.jinyiyun.framework.service.UserService;
import net.jinyiyun.framework.survey.enums.SurveyCycleEnum;
import net.jinyiyun.framework.survey.enums.SurveyStateEnum;
import net.jinyiyun.framework.survey.service.SurveyAnswerService;
import net.jinyiyun.framework.survey.service.SurveyPartnerService;
import net.jinyiyun.framework.survey.service.SurveyService;
import net.jinyiyun.framework.survey.service.model.BO.SurveyBO;
import net.jinyiyun.framework.survey.service.model.command.SurveyAnswerAddCommand;
import net.jinyiyun.framework.survey.service.model.query.SurveyAnswerBOPagerQuery;
import net.jinyiyun.framework.survey.service.model.query.SurveyPartnerBOPagerQuery;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class WeSurveySupport {

    private final PartnerMainService partnerMainService;

    private final SurveyService surveyService;

    private final SurveyAnswerService surveyAnswerService;

    private final SurveyPartnerService surveyPartnerService;

    private final OrderService orderService;

    private final AreaService areaService;

    private final UserService userService;


    /**
     * 获取用户需要填写的问卷列表
     */
    public List<WeSurveyVO> getSurveysToFill(User user) {

        // 获取用户合伙人信息
        PartnerBO partnerBO = partnerMainService.getById(user.getPartnerId());
        if (ObjectUtil.isNull(partnerBO)) {
            throw new AlertException("用户未绑定合伙人");
        }
        // 获取当前时间
        LocalDate now = LocalDate.now();
        // 获取合伙人下参与问卷列表
        List<SurveyBO> allSurveys = surveyService.selectByPartnerId(partnerBO.getId(), now);
        if (ObjectUtil.isNull(allSurveys)) {
            return Lists.newArrayList();
        }
        // 筛选出在具体投放日期之后的问卷
        List<SurveyBO> filteredSurveys = allSurveys.stream().filter(surveyBO -> {
            String deliveryCycle = surveyBO.getDeliveryCycle().getCode();
            Integer cycleDay = surveyBO.getCycleDay();
            LocalDate targetDate = calcTargetDate(deliveryCycle, cycleDay, now);

            return !now.isBefore(targetDate);
        }).collect(Collectors.toList());

        List<SurveyBO> data = filteredSurveys.stream().filter(
                surveyBO -> {
                    // 获取当前周期标识
                    String currentCycle = getCurrentCycleIdentifier(surveyBO.getDeliveryCycle().getCode());

                    // 验证是否填写过该问卷
                    long count = surveyAnswerService.count(SurveyAnswerBOPagerQuery.builder()
                            .surveyIds(Collections.singletonList(surveyBO.getId()))
                            .cycleIdentifier(currentCycle)
                            .companyTitle(partnerBO.getCompanyTitle())
                            .build());
                    if (count > 0) {
                        return false;
                    }
                    // 判断合伙人是否符合填写条件
                    return isPartnerQualified(partnerBO, surveyBO);

                }
        ).collect(Collectors.toList());

        // 筛选出每个投放周期最新的问卷
        List<SurveyBO> filterData = data.stream().collect(Collectors.groupingBy(
                        surveyBO -> surveyBO.getDeliveryCycle().getCode(),
                        Collectors.maxBy(Comparator.comparing(SurveyBO::getCreatedAt))
                )).values().stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
        return WeSurveyConvert.toVOList(filterData);
    }

    /**
     * 计算目标日期
     */
    private LocalDate calcTargetDate(String deliveryCycle, Integer cycleDay, LocalDate now) {
        SurveyCycleEnum cycleEnum = SurveyCycleEnum.getByCode(deliveryCycle);
        if (ObjectUtil.isNull(cycleEnum)) {
            throw new AlertException("未知的问卷投放周期");
        }
        switch (cycleEnum) {
            case MONTHLY:
                return calcMonthlyDate(cycleDay, now);
            case QUARTERLY:
                return calcQuarterlyDate(cycleDay, now);
            default:
                throw new AlertException("未知的问卷投放周期");
        }
    }

    /**
     * 计算每月问卷投放目标日期
     */
    private LocalDate calcMonthlyDate(Integer cycleDay, LocalDate now) {
        LocalDate firstDayOfMonth = LocalDate.of(now.getYear(), now.getMonthValue(), 1);
        return firstDayOfMonth.plusDays(cycleDay - 1);
    }

    /**
     * 计算每季度问卷投放目标日期
     */
    private LocalDate calcQuarterlyDate(Integer cycleDay, LocalDate now) {
        int quarter = (now.getMonthValue() - 1) / 3;
        LocalDate firstDayOfQuarter = LocalDate.of(now.getYear(), 3 * quarter + 1, 1);
        return firstDayOfQuarter.plusDays(cycleDay - 1);
    }

    /**
     * 获取用户信息
     */
    public WeSurveyUserInfoVO getUserInfo(User user) {
        PartnerBO partnerBO = partnerMainService.getById(user.getPartnerId());
        if (ObjectUtil.isNull(partnerBO)) {
            throw new AlertException("用户未绑定合伙人");
        }
        // todo
        String area = areaService.getById(partnerBO.getAreaId()).getTitle();
        String businessContact = userService.getById(partnerBO.getBusinessId()).getUsername();
        String companyTitle = partnerBO.getCompanyTitle();

        return WeSurveyUserInfoVO.builder()
                .area(area)
                .businessContact(businessContact)
                .companyTitle(companyTitle)
                .build();
    }

    /**
     * 提交问卷
     */
    public void submit(SurveyAnswerAddRequest request) {
        User user = Auth.getUserThrE();
        PartnerBO partnerBO = partnerMainService.getById(user.getPartnerId());
        if (ObjectUtil.isNull(partnerBO)) {
            throw new AlertException("用户未绑定合伙人");
        }
        SurveyBO surveyBO = surveyService.getById(request.getSurveyId());
        if (ObjectUtil.isNull(surveyBO) || surveyBO.getState() != SurveyStateEnum.OPEN) {
            throw new AlertException("问卷不存在或未启用");
        }
        long partnerCount = surveyPartnerService.count(SurveyPartnerBOPagerQuery.builder()
                .surveyIds(Collections.singletonList(request.getSurveyId()))
                .partnerIds(Collections.singletonList(partnerBO.getId()))
                .build());
        if (partnerCount <= 0) {
            throw new AlertException("用户未授权填写问卷");
        }
        // 获取问卷当前周期
        String currentCycle = getCurrentCycleIdentifier(surveyBO.getDeliveryCycle().getCode());
        // 验证是否填写过该问卷
        long count = surveyAnswerService.count(SurveyAnswerBOPagerQuery.builder()
                .surveyIds(Collections.singletonList(request.getSurveyId()))
                .cycleIdentifier(currentCycle)
                .companyTitle(request.getCompanyTitle())
                .build());
        if (count > 0) {
            throw new AlertException("用户已填写过该问卷");
        }
        // 验证问卷答案
        validateQuestionAnswers(surveyBO.getQuestions(), request.getData());
        SurveyAnswerAddCommand addCommand = WeSurveyAnswerConvert.toAddCommand(request);
        addCommand.setId(IdWorker.getId());
        addCommand.setUserId(user.getId());
        addCommand.setCycleIdentifier(currentCycle);
        // 保存问卷答案
        surveyAnswerService.add(addCommand);
    }



    /**
     * 获取当前周期标识
      */
    public String getCurrentCycleIdentifier(String deliveryCycle) {
        LocalDate now = LocalDate.now();

        if (SurveyCycleEnum.QUARTERLY.getCode().equals(deliveryCycle)) {
            int quarter = (now.getMonthValue() - 1) / 3 + 1;
            return now.getYear() + deliveryCycle + quarter;
        } else if (SurveyCycleEnum.MONTHLY.getCode().equals(deliveryCycle)) {
            return now.getYear() + deliveryCycle + String.format("%02d", now.getMonthValue());
        }
        throw new AlertException("不支持的投放周期类型: " + deliveryCycle);
    }

    /**
     * 判断时间是否在当前周期
     */
    public boolean isCreatedInCurrentCycle(LocalDate createdDate, String deliveryCycle) {
        LocalDate now = LocalDate.now();

        if (SurveyCycleEnum.QUARTERLY.getCode().equals(deliveryCycle)) {
            int currentQuarter = (now.getMonthValue() - 1) / 3 + 1;
            int createdQuarter = (createdDate.getMonthValue() - 1) / 3 + 1;
            return createdDate.getYear() == now.getYear() &&
                    createdQuarter == currentQuarter;
        } else if (SurveyCycleEnum.MONTHLY.getCode().equals(deliveryCycle)) {
            return createdDate.getYear() == now.getYear() &&
                    createdDate.getMonth() == now.getMonth();
        }
        return false;
    }

    /**
     * 判断合伙人是否符合填写条件
     */
    public boolean isPartnerQualified(PartnerBO partnerBO, SurveyBO surveyBO) {
        boolean isNewPartner = isCreatedInCurrentCycle(partnerBO.getCreatedAt()
                .toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                surveyBO.getDeliveryCycle().getCode());
        if (!isNewPartner) {
            return true;
        }

        // todo 是新合伙人需要判断是否下过单（下单指用户只要有生成的订单）
        Long count = orderService.lambdaQuery()
                .eq(Order::getPartnerId, partnerBO.getId())
                .count();
        return count > 0;
    }

    /**
     * 验证问题答案
     */
    public void validateQuestionAnswers(List<SurveyQuestion> questions, HashMap<String, Object> data) {
        // 验证必填问题
        for (SurveyQuestion question : questions) {
            if (question.getIsRequired() == 1) {
                String fieldName = question.getFieldNameEn();
                if (!data.containsKey(fieldName) || ObjectUtil.isNull(data.get(fieldName))) {
                    throw new AlertException("问题[" + question.getDescription() + "]为必填项");
                }
            }
        }
    }
}
