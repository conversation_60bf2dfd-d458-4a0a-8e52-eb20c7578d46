package net.jinyiyun.app.business.controller.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.jinyiyun.app.business.support.order.BackOrderSupport;
import net.jinyiyun.app.common.OrderStockSupport;
import net.jinyiyun.app.common.model.VO.OrderStockCheckVO;
import net.jinyiyun.app.common.model.VO.value.OrderStockInsufficientItemVO;
import net.jinyiyun.app.common.model.request.OrderStockCheckRequest;
import net.jinyiyun.app.excel.BusinessUploadOrderExcelDataListener;
import net.jinyiyun.app.excel.BusinessUploadOrderExcelItem;
import net.jinyiyun.app.service.BusinessHelpService;
import net.jinyiyun.common.lib.MbQueryLib;
import net.jinyiyun.common.security.Auth;
import net.jinyiyun.common.utils.ExpressTrackingRequest;
import net.jinyiyun.common.utils.JacksonUtil;
import net.jinyiyun.common.utils.SpesUtil;
import net.jinyiyun.config.exception.AlertException;
import net.jinyiyun.config.request.PageReq;
import net.jinyiyun.config.resolver.datetimerange.LocalDateTimeRange;
import net.jinyiyun.config.resolver.datetimerange.LocalDateTimeRangeFormat;
import net.jinyiyun.config.response.ExpressTrackingResponse;
import net.jinyiyun.config.response.Resp;
import net.jinyiyun.framework.common.Response;
import net.jinyiyun.framework.common.annotation.PreventResubmission;
import net.jinyiyun.framework.common.enums.ResubmissionStrategyEnum;
import net.jinyiyun.framework.dto.OrderAllInfoResp;
import net.jinyiyun.framework.dto.OrderDeliveryBo;
import net.jinyiyun.framework.dto.app.businessbatchaddress.OrderBatchSubmitReq;
import net.jinyiyun.framework.dto.app.businessbatchaddress.OrderSelect;
import net.jinyiyun.framework.dto.app.businessbatchaddress.OrderUploadExcel;
import net.jinyiyun.framework.dto.app.order.OrderCheckReq;
import net.jinyiyun.framework.dto.app.order.OrderConfirmReq;
import net.jinyiyun.framework.dto.app.order.OrderConfirmResp;
import net.jinyiyun.framework.entity.Order;
import net.jinyiyun.framework.entity.OrderDelivery;
import net.jinyiyun.framework.entity.Partner;
import net.jinyiyun.framework.entity.User;
import net.jinyiyun.framework.entity.base.BaseEntity;
import net.jinyiyun.framework.query.OrdersQuery;
import net.jinyiyun.framework.service.GoodsService;
import net.jinyiyun.framework.service.OrderDeliveryService;
import net.jinyiyun.framework.service.OrderService;
import net.jinyiyun.framework.service.PartnerService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 订单管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("business/order")
@Slf4j
@RequiredArgsConstructor
@PreAuthorize("hasAnyAuthority('ROLE_BUSINESS_MANAGER','ROLE_BUSINESS_MEMBER')")
public class BackBusinessOrderController {

    private final OrderService orderService;
    private final BusinessHelpService businessHelpService;
    private final OrderDeliveryService orderDeliveryService;
    private final GoodsService goodsService;
    private final PartnerService partnerService;
    private final OrderStockSupport orderStockSupport;
    private final BackOrderSupport backOrderSupport;


    @PreAuthorize("hasAuthority('order')")
    @GetMapping
    public Resp index(PageReq pageDto,
                      Order dto,
                      @LocalDateTimeRangeFormat("createdAtRange") LocalDateTimeRange createdAtRange,
                      OrdersQuery query) {
        QueryWrapper<Order> wrapper = MbQueryLib.getQueryWrapper(dto);
        Set<Long> partnerIds = businessHelpService.getPartnerIds(Auth.getUserThrE(User.class));
        if (CollUtil.isEmpty(partnerIds)) {
            return Resp.success();
        }
        wrapper.in("partner_id", partnerIds);

        if (createdAtRange != null) {
            wrapper.between("created_at", createdAtRange.getStart(), createdAtRange.getEnd());
        }
        if (ObjectUtil.isNotNull(query.getBusinessId())) {
            // 查询这个业务员下合伙人
            List<Long> partnerIdList = partnerService.lambdaQuery().eq(Partner::getBusinessId, query.getBusinessId()).list().stream().map(BaseEntity::getId).collect(Collectors.toList());
            partnerIdList.add(-1L);
            wrapper.in("partner_id", partnerIdList);
        }

        wrapper.ge(query.getPayAmountRangeMin() != null, "pay_amount", query.getPayAmountRangeMin());
        wrapper.le(query.getPayAmountRangeMax() != null, "pay_amount", query.getPayAmountRangeMax());

        if (query.getDeliveryId() != null) {
            wrapper.exists("select 1 from order_deliveries where order_deliveries.order_id = orders.id and order_deliveries.id = {0}", query.getDeliveryId());
        }
        if (CharSequenceUtil.isNotBlank(query.getJyNo())) {
            wrapper.exists("select 1 from order_deliveries where order_deliveries.order_id = orders.id and order_deliveries.jy_no = {0}", query.getJyNo());
        }
        if (CollectionUtil.isNotEmpty(query.getStateList())) {
            wrapper.in("state", query.getStateList());
        }

        // 是否售后的查询条件
        if (ObjectUtil.isNotNull(query.getIsAfterSale())) {
            String afterSaleSubQuery =
                    "select 1 from order_deliveries od " +
                            "where od.order_id = orders.id " +
                            "and (" +
                            "exists (select 1 from exchange_delivery ed where ed.delivery_id = od.id and ed.audit_state = 1) " +
                            "or exists (select 1 from reimbursement_refunds rr where rr.delivery_id = od.id and rr.audit_state = 1) " +
                            "or exists (select 1 from wrong_miss_delivery wmd where wmd.delivery_id = od.id and wmd.audit_state = 1)" +
                            ")";
            if (query.getIsAfterSale() == 1) {
                wrapper.exists(afterSaleSubQuery);
            } else {
                wrapper.notExists(afterSaleSubQuery);
            }
        }

        // 售后单号查询条件
        if (CharSequenceUtil.isNotBlank(query.getNo())) {
            wrapper.exists(
                    "select 1 from order_deliveries od " +
                            "where od.order_id = orders.id " +
                            "and (" +
                            "exists (select 1 from exchange_delivery ed where ed.delivery_id = od.id and ed.return_change_no = {0}) " +
                            "or exists (select 1 from reimbursement_refunds rr where rr.delivery_id = od.id and rr.refund_no = {0}) " +
                            "or exists (select 1 from wrong_miss_delivery wmd where wmd.delivery_id = od.id and wmd.disorder_no = {0})" +
                            ")", query.getNo());
        }

        // 售后审批状态查询条件
        if (ObjectUtil.isNotNull(query.getAuditState())) {
            wrapper.exists(
                    "select 1 from order_deliveries od " +
                            "where od.order_id = orders.id " +
                            "and (" +
                            "exists (select 1 from exchange_delivery ed where ed.delivery_id = od.id and ed.audit_state = {0}) " +
                            "or exists (select 1 from reimbursement_refunds rr where rr.delivery_id = od.id and rr.audit_state = {0}) " +
                            "or exists (select 1 from wrong_miss_delivery wmd where wmd.delivery_id = od.id and wmd.audit_state = {0})" +
                            ")", query.getAuditState());
        }

        return Resp.success(orderService.page(pageDto.toMpPage(), wrapper));
    }


    /**
     * 订单详情
     *
     * @param id 订单id
     */
    @PreAuthorize("hasAuthority('order:detail')")
    @GetMapping("/{id}")
    public Resp detail(@PathVariable Long id) {
        businessHelpService.getOrder(Auth.getUserThrE(), id);
        OrderAllInfoResp order = orderService.getOrderAllInfoById(id);
        return Resp.success(order);
    }


    /**
     * 发货单信息
     *
     * @param id 订单id
     */
    @PreAuthorize("hasAuthority('order:delivery')")
    @GetMapping("{id}/delivery")
    public Response<List<OrderDeliveryBo>> delivery(@PathVariable Long id) {
        businessHelpService.getOrder(Auth.getUserThrE(), id);
        return Response.success(orderDeliveryService.listByOrderId(id));
    }

    /**
     * 取消订单
     *
     * @param closeReason 取消原因
     * @param id          订单id
     */
    @PreAuthorize("hasAuthority('order:cancel')")
    @GetMapping("/{id}/cancel")
    @PreventResubmission(
            keyPrefix = "ORDER_CANCEL",
            interval = 10,
            timeUnit = TimeUnit.SECONDS,
            strategy = ResubmissionStrategyEnum.ID_METHOD_SIGNATURE,
            idSpEL = "#id"
    )
    public Resp cancel(@RequestParam String closeReason, @PathVariable Long id) {

        Order order = businessHelpService.getOrder(Auth.getUserThrE(), id);
        // 查询订单是否已取消，如果已取消，返回
        Integer state = order.getState();
        if (Order.State.CANCEL.getCode().equals(state)) {
            throw new AlertException("该订单已取消");
        }
        // 从吉客云拉取已同步的发货单最新信息,如果有任何已经发货的订单则不允许取消
        orderService.cancelJkyGetOrderDeliveryByOrderId(order.getId());
        // 取消订单
        orderService.cancelOrder(order, closeReason);
        return Resp.success();
    }

    /**
     * 重试同步出错的发货单
     *
     * @param id 订单id
     */

    @GetMapping("/{id}/sync/error/retry")
    @PreventResubmission(
            keyPrefix = "ORDER_SYNC_ERROR_RETRY",
            interval = 10,
            timeUnit = TimeUnit.SECONDS,
            strategy = ResubmissionStrategyEnum.ID_METHOD_SIGNATURE,
            idSpEL = "#id"
    )
    public Resp syncErrorRetry(@PathVariable Long id) {
        Order order = businessHelpService.getOrder(Auth.getUserThrE(), id);
        orderService.jkyCreateByOrderId(order.getId());
        return Resp.success();
    }


    /**
     * 从吉客云拉取该订单最新数据
     */
    @PreAuthorize("hasAuthority('order:sync')")
    @GetMapping("/{id}/sync")
    @PreventResubmission(
            keyPrefix = "ORDER_SYNC",
            interval = 10,
            timeUnit = TimeUnit.SECONDS,
            strategy = ResubmissionStrategyEnum.ID_METHOD_SIGNATURE,
            idSpEL = "#id"
    )
    public Resp sync(@PathVariable Long id) {
        Order order = businessHelpService.getOrder(Auth.getUserThrE(), id);
        orderService.jkyGetOrderDeliveryByOrderId(order.getId());
        return Resp.success();
    }

    /**
     * 将已经创建了吉客云单号的发货单，同步到系统
     */
    @PreAuthorize("hasAuthority('order:sync')")
    @GetMapping("/jkySync/{id}")
    @PreventResubmission(
            keyPrefix = "ORDER_SYNC_DELIVERY",
            interval = 10,
            timeUnit = TimeUnit.SECONDS,
            strategy = ResubmissionStrategyEnum.ID_METHOD_SIGNATURE,
            idSpEL = "#id"
    )
    public Resp syncDelivery(@PathVariable Long id) {
        backOrderSupport.syncOrder(id);
        return Resp.success();
    }


    /**
     * 代合伙人下单 批量地址 选择商品
     */
    @PreAuthorize("hasAuthority('order:agent')")
    @PostMapping("batch/select/goods")
    public Resp batchSelectGoods(OrderUploadExcel uploadOrderExcel) {
        User user = Auth.getUserThrE();
        businessHelpService.getPartner(user, uploadOrderExcel.getPartnerId());

        if (uploadOrderExcel.getFile() == null) {
            return Resp.error("请上传文件！");
        }
        // 解析excel
        InputStream inputStream;
        try {
            inputStream = uploadOrderExcel.getFile().getInputStream();
        } catch (Exception e) {
            throw new AlertException("文件读取失败!");
        }
        BusinessUploadOrderExcelDataListener listener = new BusinessUploadOrderExcelDataListener(goodsService, uploadOrderExcel.getPartnerId());
        EasyExcel.read(inputStream, BusinessUploadOrderExcelItem.class, listener).sheet().doRead();
        List<OrderSelect> orderSelects = listener.getReturnData();
        return Resp.success(orderSelects);
    }

    /**
     * 订单库存确认
     */
    @PreAuthorize("hasAuthority('order:agent')")
    @PostMapping("/stockCheck")
    public Response<OrderStockCheckVO> stock(@RequestBody @Validated OrderStockCheckRequest request) {
        List<OrderStockInsufficientItemVO> orderStockInsufficient = orderStockSupport.orderStock(request);
        return Response.success(OrderStockCheckVO.builder()
                .items(orderStockInsufficient).build());
    }

    /**
     * 检验订单 (提交前)
     */
    @PreAuthorize("hasAuthority('order:agent')")
    @PostMapping("check")
    public Resp check(@RequestBody @Valid OrderCheckReq checkReq) {
        User user = Auth.getUserThrE();
        return orderService.check(checkReq, user.getId(), businessHelpService.getPartner(user, checkReq.getPartnerId()));
    }


    /**
     * 确认订单
     */
    @PreAuthorize("hasAuthority('order:agent')")
    @PostMapping("confirm")
    public Resp create(@RequestBody @Valid OrderConfirmReq confirmReq) {
        User user = Auth.getUserThrE();
        OrderConfirmResp resp = orderService.orderConfirm(confirmReq, user.getId(), businessHelpService.getPartner(user, confirmReq.getPartnerId()));
        return Resp.success(resp);
    }



    /**
     * 提交并支付订单
     */
    @PreAuthorize("hasAuthority('order:agent')")
    @PostMapping("submit")
    @PreventResubmission(
            keyPrefix = "ORDER_SUBMIT",
            interval = 10,
            timeUnit = TimeUnit.SECONDS,
            strategy = ResubmissionStrategyEnum.ID_METHOD_SIGNATURE,
            idSpEL = "#batchSubmitReq.orderId"
    )
    public Resp submit(@RequestBody @Valid OrderBatchSubmitReq batchSubmitReq) {
        User user = Auth.getUserThrE();
        Partner partner = businessHelpService.getPartner(user, batchSubmitReq.getPartnerId());
        Order order = orderService.batchAddressSubmitAndPay(batchSubmitReq, user.getId(), partner);
        // 同步吉客云
        try {
            orderService.jkyCreateByOrderId(order.getId());
        } catch (Exception e) {
            log.error("同步吉客云失败", e);
        }
        return Resp.success(order);
    }


    /**
     * 商家备注追加
     */
    @PatchMapping("/edit")
    public Resp update(@RequestBody Order order) {
        User user = Auth.getUserThrE(User.class);
        orderService.edit(order,user.getUsername());
        return Resp.success();
    }

    /**
     * 查询该订单下，可以申请售后的发货单列表
     */
    @GetMapping("/can/aftersale")
    @PreAuthorize("hasAuthority('order:detail')")
    public Resp getCanAfterSale(Order order) {

        Long orderId = order.getId();
        List<Integer> ids = new ArrayList<>();
        // 4112，5010，6000，9090
        ids.add(4112);
        ids.add(5010);
        ids.add(6000);
        ids.add(9090);
        List<OrderDelivery> list = orderDeliveryService.lambdaQuery().eq(OrderDelivery::getOrderId, orderId)
                .in(OrderDelivery::getState, ids).list();
        if (CollUtil.isEmpty(list)) {
            return Resp.success(list);
        }
        ArrayList<OrderDeliveryBo> orderDeliveryBos = new ArrayList<>();
        for (OrderDelivery orderDelivery : list) {
            OrderDeliveryBo orderDeliveryBo = new OrderDeliveryBo();
            BeanUtil.copyProperties(orderDelivery,orderDeliveryBo);
            Integer state = orderDelivery.getState();
            if (4112 == state) {
                orderDeliveryBo.setAfterSale(Collections.singletonList(0));
            }
            if (5010 == state) {
                orderDeliveryBo.setAfterSale(Arrays.asList(0,1));

            }
            if (6000 == state) {
                orderDeliveryBo.setAfterSale(Arrays.asList(0,1,2));

            }
            if (9090 == state) {
                orderDeliveryBo.setAfterSale(Arrays.asList(0,1,2));
            }
            orderDeliveryBos.add(orderDeliveryBo);
        }


        return Resp.success(orderDeliveryBos);
    }

    /**
     * 根据发货单ID，查询发货单详情列表
     */
    @GetMapping("/aftersale/delivery/detail")
    @PreAuthorize("hasAuthority('order:detail')")
    public Resp getCanAfterSale(Long deliveryId) {
        return Resp.success(orderDeliveryService.getOrderDeliveryDetailByDeliveryId(deliveryId));
    }

    /**
     * 订单发货单批量导出
     */
    @GetMapping("/export")
    public void export(Order dto,
                       @LocalDateTimeRangeFormat("createdAtRange") LocalDateTimeRange createdAtRange,
                       OrdersQuery query, HttpServletResponse response) throws IOException {
        orderService.export(response, dto, createdAtRange, query);
    }

    /**
     * 物流查询
     */
    @GetMapping("/express/tracking")
    public Resp expressTracking(String number, @RequestParam(required = false) String mobile) {
        // 若传了手机号，则获取手机号后四位
        String lastFourDigitsOfMobile = SpesUtil.getLastFourDigits(mobile);
        String json = ExpressTrackingRequest.parse(number, lastFourDigitsOfMobile);
        ExpressTrackingResponse expressTrackingResponse = JacksonUtil.str2obj(json, ExpressTrackingResponse.class);
        if (expressTrackingResponse.getCode() != 0){
            throw new AlertException(expressTrackingResponse.getDesc());
        }
        return Resp.success(expressTrackingResponse.getData());
    }

}
