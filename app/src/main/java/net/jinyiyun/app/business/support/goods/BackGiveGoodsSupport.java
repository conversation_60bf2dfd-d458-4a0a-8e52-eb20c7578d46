package net.jinyiyun.app.business.support.goods;


import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.jinyiyun.app.business.support.goods.model.VO.GiveGoodsVO;
import net.jinyiyun.app.business.support.goods.model.convert.GiveGoodSupportConvert;
import net.jinyiyun.app.business.support.goods.model.query.GiveGoodsPagerQuery;
import net.jinyiyun.app.business.support.goods.model.request.GiveGoodsAddRequest;
import net.jinyiyun.app.business.support.goods.model.request.GiveGoodsEditRequest;
import net.jinyiyun.framework.common.Pager;
import net.jinyiyun.framework.goods.service.GiveGoodService;
import net.jinyiyun.framework.goods.service.model.BO.GiveGoodBO;
import net.jinyiyun.framework.goods.service.model.query.GiveGoodBOPagerQuery;
import net.jinyiyun.framework.goods.service.model.request.GiveGoodAddCommand;
import net.jinyiyun.framework.goods.service.model.request.GiveGoodUpdateCommand;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class BackGiveGoodsSupport {
    private final GiveGoodService giveGoodService;

    /**
     * 新增赠品
     */
    public void add(GiveGoodsAddRequest request) {
        GiveGoodAddCommand command = GiveGoodSupportConvert.toAddCommand(request);
        long id = IdWorker.getId();
        command.setId(id);
        giveGoodService.add(command);
    }

    /**
     * 删除赠品
     */
    public void delete(Long id) {
        giveGoodService.delete(id);
    }

    /**
     * 更新赠品
     */
    public void update(GiveGoodsEditRequest request) {
        GiveGoodUpdateCommand command = GiveGoodSupportConvert.toUpdateCommand(request);
        giveGoodService.update(command);
    }

    public Pager<GiveGoodsVO> page(GiveGoodsPagerQuery query) {
        GiveGoodBOPagerQuery pageQuery = GiveGoodSupportConvert.toQuery(query);
        Pager<GiveGoodBO> giveGoods = giveGoodService.list(pageQuery);
        if (giveGoods.getTotal() == 0) {
            return new Pager<>(query.getPage(), query.getPageSize(), 0, null);
        }
        return new Pager<>(query.getPage(),
                query.getPageSize(),
                giveGoods.getTotal(),
                GiveGoodSupportConvert.toVOList(giveGoods.getData()));
    }
}
