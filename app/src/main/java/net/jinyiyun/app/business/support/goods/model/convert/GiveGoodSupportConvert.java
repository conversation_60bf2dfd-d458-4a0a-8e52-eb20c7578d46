package net.jinyiyun.app.business.support.goods.model.convert;

import cn.hutool.core.collection.CollUtil;
import net.jinyiyun.app.business.support.goods.model.VO.GiveGoodsVO;
import net.jinyiyun.app.business.support.goods.model.query.GiveGoodsPagerQuery;
import net.jinyiyun.app.business.support.goods.model.request.GiveGoodsAddRequest;
import net.jinyiyun.app.business.support.goods.model.request.GiveGoodsEditRequest;
import net.jinyiyun.framework.common.BasePager;
import net.jinyiyun.framework.goods.enums.CommonGoodStateEnum;
import net.jinyiyun.framework.goods.service.model.BO.GiveGoodBO;
import net.jinyiyun.framework.goods.service.model.query.GiveGoodBOPagerQuery;
import net.jinyiyun.framework.goods.service.model.request.GiveGoodAddCommand;
import net.jinyiyun.framework.goods.service.model.request.GiveGoodUpdateCommand;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public interface GiveGoodSupportConvert {

    static GiveGoodsVO toVO(GiveGoodBO giveGoodBO) {
        return GiveGoodsVO.builder()
                .id(giveGoodBO.getId())
                .no(giveGoodBO.getNo())
                .barcode(giveGoodBO.getBarcode())
                .title(giveGoodBO.getTitle())
                .sales(giveGoodBO.getSales())
                .state(giveGoodBO.getState().getCode())
                .createdAt(giveGoodBO.getCreatedAt())
                .updatedAt(giveGoodBO.getUpdatedAt())
                .build();
    }

    static GiveGoodAddCommand toAddCommand(GiveGoodsAddRequest request) {
        return GiveGoodAddCommand.builder()
                .no(request.getNo())
                .barcode(request.getBarcode())
                .title(request.getTitle())
                .state(CommonGoodStateEnum.getByCode(request.getState()))
                .build();
    }

    static GiveGoodUpdateCommand toUpdateCommand(GiveGoodsEditRequest request) {
        return GiveGoodUpdateCommand.builder()
                .id(request.getId())
                .no(request.getNo())
                .barcode(request.getBarcode())
                .title(request.getTitle())
                .state(CommonGoodStateEnum.getByCode(request.getState()))
                .build();
    }

    static GiveGoodBOPagerQuery toQuery(GiveGoodsPagerQuery query) {
        return GiveGoodBOPagerQuery.builder()
                .titleSearch(StringUtils.isNotBlank(query.getTitle()) ? query.getTitle() : null)
                .nos(StringUtils.isNotBlank(query.getNo()) ? Collections.singletonList(query.getNo()) : null)
                .barcodes(StringUtils.isNotBlank(query.getBarcode()) ? Collections.singletonList(query.getBarcode())
                        : null)
                .sales(query.getSales() != null ? Collections.singletonList(query.getSales()) : null)
                .states(query.getState() != null ?
                        Collections.singletonList(CommonGoodStateEnum.getByCode(query.getState())) : null)
                .pager(new BasePager.Pager(query.getPage(), query.getPageSize()))
                .build();
    }

    static List<GiveGoodsVO> toVOList(List<GiveGoodBO> giveGoodBOList) {
        if (CollUtil.isEmpty(giveGoodBOList)) {
            return Collections.emptyList();
        }
        return giveGoodBOList.stream().map(GiveGoodSupportConvert::toVO).collect(Collectors.toList());
    }

}
