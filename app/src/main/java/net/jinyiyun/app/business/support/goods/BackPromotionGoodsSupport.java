package net.jinyiyun.app.business.support.goods;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.jinyiyun.app.business.support.goods.model.VO.PromotionGoodsVO;
import net.jinyiyun.app.business.support.goods.model.convert.PromotionGoodSupportConvert;
import net.jinyiyun.app.business.support.goods.model.query.PromotionGoodsPagerQuery;
import net.jinyiyun.app.business.support.goods.model.request.PromotionGoodsAddRequest;
import net.jinyiyun.app.business.support.goods.model.request.PromotionGoodsEditRequest;
import net.jinyiyun.framework.common.Pager;
import net.jinyiyun.framework.goods.service.PromotionGoodService;
import net.jinyiyun.framework.goods.service.model.BO.PromotionGoodBO;
import net.jinyiyun.framework.goods.service.model.query.PromotionGoodBOPagerQuery;
import net.jinyiyun.framework.goods.service.model.request.PromotionGoodAddCommand;
import net.jinyiyun.framework.goods.service.model.request.PromotionGoodUpdateCommand;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class BackPromotionGoodsSupport {

    private final PromotionGoodService promotionGoodsService;

    /**
     * 新增促销品
     */
    public void add(PromotionGoodsAddRequest request) {
        PromotionGoodAddCommand addCommand = PromotionGoodSupportConvert.toAddCommand(request);
        addCommand.setId(IdWorker.getId());
        promotionGoodsService.add(addCommand);
    }

    /**
     * 删除促销品
     */
    public void delete(Long id) {
        promotionGoodsService.delete(id);
    }

    /**
     * 更新促销品
     */
    public void update(PromotionGoodsEditRequest request) {
        PromotionGoodUpdateCommand updateCommand = PromotionGoodSupportConvert.toUpdateCommand(request);
        promotionGoodsService.update(updateCommand);
    }

    public Pager<PromotionGoodsVO> pager(PromotionGoodsPagerQuery query) {
        PromotionGoodBOPagerQuery pagerQuery = PromotionGoodSupportConvert.toQuery(query);
        Pager<PromotionGoodBO> goods = promotionGoodsService.list(pagerQuery);
        if (goods.getTotal() == 0) {
            return new Pager<>(query.getPage(), query.getPageSize(), 0, null);
        }
        return new Pager<>(query.getPage(),
                query.getPageSize(),
                goods.getTotal(),
                goods.getData().stream().map(PromotionGoodSupportConvert::toVO).collect(Collectors.toList()));
    }
}
