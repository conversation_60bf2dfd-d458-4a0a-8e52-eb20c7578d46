package net.jinyiyun.app.business.support.goods.model.convert;

import cn.hutool.core.collection.CollUtil;
import net.jinyiyun.app.business.support.goods.model.VO.PromotionGoodsVO;
import net.jinyiyun.app.business.support.goods.model.query.PromotionGoodsPagerQuery;
import net.jinyiyun.app.business.support.goods.model.request.PromotionGoodsAddRequest;
import net.jinyiyun.app.business.support.goods.model.request.PromotionGoodsEditRequest;
import net.jinyiyun.framework.common.BasePager;
import net.jinyiyun.framework.goods.enums.CommonGoodStateEnum;
import net.jinyiyun.framework.goods.service.model.BO.PromotionGoodBO;
import net.jinyiyun.framework.goods.service.model.query.PromotionGoodBOPagerQuery;
import net.jinyiyun.framework.goods.service.model.request.PromotionGoodAddCommand;
import net.jinyiyun.framework.goods.service.model.request.PromotionGoodUpdateCommand;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public interface PromotionGoodSupportConvert {

    static PromotionGoodsVO toVO(PromotionGoodBO promotionGoodBO) {
        return PromotionGoodsVO.builder()
                .id(promotionGoodBO.getId())
                .no(promotionGoodBO.getNo())
                .barcode(promotionGoodBO.getBarcode())
                .title(promotionGoodBO.getTitle())
                .sales(promotionGoodBO.getSales())
                .state(promotionGoodBO.getState().getCode())
                .createdAt(promotionGoodBO.getCreatedAt())
                .updatedAt(promotionGoodBO.getUpdatedAt())
                .build();
    }

    static PromotionGoodAddCommand toAddCommand(PromotionGoodsAddRequest request) {
        return PromotionGoodAddCommand.builder()
                .no(request.getNo())
                .barcode(request.getBarcode())
                .title(request.getTitle())
                .state(CommonGoodStateEnum.getByCode(request.getState()))
                .build();
    }

    static PromotionGoodUpdateCommand toUpdateCommand(PromotionGoodsEditRequest request) {
        return PromotionGoodUpdateCommand.builder()
                .id(request.getId())
                .no(request.getNo())
                .barcode(request.getBarcode())
                .title(request.getTitle())
                .state(CommonGoodStateEnum.getByCode(request.getState()))
                .build();
    }

    static PromotionGoodBOPagerQuery toQuery(PromotionGoodsPagerQuery query) {
        return PromotionGoodBOPagerQuery.builder()
                .nos(query.getNo() != null ? Collections.singletonList(query.getNo()) : null)
                .barcodes(query.getBarcode() != null ? Collections.singletonList(query.getBarcode()) : null)
                .titleSearch(query.getTitle())
                .states(query.getState() != null ?
                        Collections.singletonList(CommonGoodStateEnum.getByCode(query.getState())) : null)
                .pager(new BasePager.Pager(query.getPage(), query.getPageSize()))
                .build();
    }

    static List<PromotionGoodsVO> toVOList(List<PromotionGoodBO> promotionGoodBOList) {
        if (CollUtil.isEmpty(promotionGoodBOList)) {
            return Collections.emptyList();
        }
        return promotionGoodBOList.stream().map(PromotionGoodSupportConvert::toVO).collect(Collectors.toList());
    }
}
