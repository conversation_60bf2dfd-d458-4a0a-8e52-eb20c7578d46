package net.jinyiyun.app.business.support.goods;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.jinyiyun.app.business.support.goods.model.VO.GoodSpecVO;
import net.jinyiyun.app.business.support.goods.model.convert.GoodSpecSupportConvert;
import net.jinyiyun.app.business.support.goods.model.query.GoodsSpecPagerQuery;
import net.jinyiyun.app.business.support.goods.model.request.GoodSpecAddRequest;
import net.jinyiyun.app.business.support.goods.model.request.GoodSpecEditRequest;
import net.jinyiyun.framework.common.BasePager;
import net.jinyiyun.framework.common.Pager;
import net.jinyiyun.framework.goods.service.GoodSpecService;
import net.jinyiyun.framework.goods.service.GoodsNewService;
import net.jinyiyun.framework.goods.service.model.BO.GoodSpecBO;
import net.jinyiyun.framework.goods.service.model.BO.GoodsBO;
import net.jinyiyun.framework.goods.service.model.query.GoodSpecBOPagerQuery;
import net.jinyiyun.framework.goods.service.model.query.GoodsBOPagerQuery;
import net.jinyiyun.framework.goods.service.model.request.GoodSpecAddCommand;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class BackGoodSpecSupport {


    private final GoodSpecService specService;

    private final GoodsNewService goodsNewService;

    /**
     * 更新商品规格
     */
    public void update(GoodSpecEditRequest request) {
        if (request == null) {
            return;
        }
        specService.update(GoodSpecSupportConvert.req2cmd(request));
    }

    /**
     * 新增商品规格
     */
    public void add(GoodSpecAddRequest request) {
        if (request == null) {
            return;
        }
        GoodSpecAddCommand command = GoodSpecSupportConvert.req2cmd(request);
        command.setId(IdWorker.getId());
        command.setSales(0);
        specService.add(command);
    }

    /**
     * 分页查询商品规格
     */
    public Pager<GoodSpecVO> page(GoodsSpecPagerQuery query) {
        GoodSpecBOPagerQuery pagerQuery = GoodSpecSupportConvert.queryConvert(query);
        if (CollUtil.isNotEmpty(query.getGoodsIds())) {
            pagerQuery.setGoodsIds(query.getGoodsIds());
        }
        long count = specService.count(pagerQuery);
        if (count == 0) {
            return new Pager<>(query.getPage(), query.getPageSize(), 0, null);
        }
        List<GoodSpecBO> goodSpec = specService.list(pagerQuery);
        if (CollUtil.isEmpty(goodSpec)) {
            return new Pager<>(query.getPage(), query.getPageSize(), 0, null);
        }
        Set<Long> goodsIds = goodSpec.stream().map(GoodSpecBO::getGoodsId).collect(Collectors.toSet());
        List<GoodsBO> goods = goodsNewService.list(GoodsBOPagerQuery.builder()
                .ids(new ArrayList<>(goodsIds))
                .pager(new BasePager.Pager(1, goodsIds.size()))
                .build());
        List<GoodSpecVO> goodSpecVOS = GoodSpecSupportConvert.bo2vo(goodSpec, goods);
        return new Pager<>(query.getPage(), query.getPageSize(), count, goodSpecVOS);
    }

}