package net.jinyiyun.app.business.support.goods.model.convert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import net.jinyiyun.app.business.support.goods.model.VO.GoodSpecVO;
import net.jinyiyun.app.business.support.goods.model.query.GoodsSpecPagerQuery;
import net.jinyiyun.app.business.support.goods.model.request.GoodSpecAddRequest;
import net.jinyiyun.app.business.support.goods.model.request.GoodSpecEditRequest;
import net.jinyiyun.framework.common.BasePager;
import net.jinyiyun.framework.goods.enums.CommonGoodStateEnum;
import net.jinyiyun.framework.goods.enums.GoodsSpecsTypeEnum;
import net.jinyiyun.framework.goods.service.model.BO.GoodSpecBO;
import net.jinyiyun.framework.goods.service.model.BO.GoodsBO;
import net.jinyiyun.framework.goods.service.model.query.GoodSpecBOPagerQuery;
import net.jinyiyun.framework.goods.service.model.request.GoodSpecAddCommand;
import net.jinyiyun.framework.goods.service.model.request.GoodSpecUpdateCommand;
import org.apache.commons.compress.utils.Lists;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class GoodSpecSupportConvert {

    public static GoodSpecUpdateCommand req2cmd(GoodSpecEditRequest req) {
        if (req == null) {
            return null;
        }
        return GoodSpecUpdateCommand.builder()
                .id(req.getId())
                .goodsId(req.getGoodsId())
                .groupId(req.getGroupId())
                .attr(req.getAttr())
                .jkyAttr(req.getJkyAttr())
                .no(req.getNo())
                .barcode(req.getBarcode())
                .pictures(req.getPictures())
                .boxSpec(req.getBoxSpec())
                .price(req.getPrice())
                .state(CommonGoodStateEnum.getByCode(req.getState()))
                .type(GoodsSpecsTypeEnum.getByCode(req.getType()))
                .build();
    }

    public static GoodSpecAddCommand req2cmd(GoodSpecAddRequest request) {
        return GoodSpecAddCommand.builder()
                .no(request.getNo())
                .barcode(request.getBarcode())
                .goodsId(request.getGoodsId())
                .attr(request.getAttr())
                .jkyAttr(request.getJkyAttr())
                .price(request.getPrice())
                .pictures(request.getPictures())
                .state(CommonGoodStateEnum.getByCode(request.getState()))
                .type(GoodsSpecsTypeEnum.getByCode(request.getType()))
                .groupId(request.getGroupId())
                .boxSpec(request.getBoxSpec())
                .build();
    }

    public static GoodSpecBOPagerQuery queryConvert(GoodsSpecPagerQuery query) {
        return GoodSpecBOPagerQuery.builder()
                .pager(new BasePager.Pager(query.getPage(), query.getPageSize()))
                .goodsIds(ObjectUtil.isNotNull(query.getGoodsId()) ?
                        Collections.singletonList(query.getGoodsId()) : null)
                .types(ObjectUtil.isNotNull(GoodsSpecsTypeEnum.getByCode(query.getType())) ?
                        Collections.singletonList(GoodsSpecsTypeEnum.getByCode(query.getType())) : null)
                .groupIds(ObjectUtil.isNotNull(query.getGroupId()) ?
                        Collections.singletonList(query.getGroupId()) : null)
                .states(ObjectUtil.isNotNull(GoodsSpecsTypeEnum.getByCode(query.getState())) ?
                        Collections.singletonList(CommonGoodStateEnum.getByCode(query.getState())) : null)
                .attrSearch(ObjectUtil.isNotNull(query.getAttr()) ? query.getAttr() : null)
                .build();
    }

    public static List<GoodSpecVO> bo2vo(List<GoodSpecBO> goodSpec) {
        if (CollUtil.isEmpty(goodSpec)) {
            return Lists.newArrayList();
        }
        return goodSpec.stream().map(GoodSpecSupportConvert::bo2vo).collect(Collectors.toList());
    }

    public static GoodSpecVO bo2vo(GoodSpecBO spec) {
        return GoodSpecVO.builder()
                .id(spec.getId())
                .no(spec.getNo())
                .barcode(spec.getBarcode())
                .goodsId(spec.getGoodsId())
                .attr(spec.getAttr())
                .jkyAttr(spec.getJkyAttr())
                .price(spec.getPrice())
                .pictures(spec.getPictures())
                .sales(spec.getSales())
                .state(ObjectUtil.isNull(spec.getState()) ? null : spec.getState().getCode())
                .type(ObjectUtil.isNull(spec.getType()) ? null : spec.getType().getCode())
                .groupId(spec.getGroupId())
                .boxSpec(spec.getBoxSpec())
                .createdAt(spec.getCreatedAt())
                .updatedAt(spec.getUpdatedAt())
                .build();
    }

    public static List<GoodSpecVO> bo2vo(List<GoodSpecBO> goodSpec, List<GoodsBO> goods) {
        if (CollUtil.isEmpty(goodSpec) || CollUtil.isEmpty(goods)) {
            return Lists.newArrayList();
        }
        Map<Long, GoodsBO> goodsMap = goods.stream().collect(Collectors.toMap(GoodsBO::getId, Function.identity()));
        return goodSpec.stream().map(spec -> {
            GoodSpecVO goodSpecVO = bo2vo(spec);
            GoodsBO goodInfo = goodsMap.get(spec.getGoodsId());
            if (goodInfo != null) {
                goodSpecVO.setGoodsTitle(goodInfo.getTitle());
                if (ObjectUtil.isNotNull(goodSpecVO.getType()) && GoodsSpecsTypeEnum.COMBINE.getCode().equals(goodSpecVO.getType())) {
                    goodSpecVO.setGoodsTitle(goodInfo.getTitle() + "[组合]");
                }
            }
            return goodSpecVO;
        }).collect(Collectors.toList());
    }
}
