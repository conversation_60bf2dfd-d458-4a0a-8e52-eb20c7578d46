package net.jinyiyun.admin.partner.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import lombok.RequiredArgsConstructor;
import net.jinyiyun.admin.partner.support.convert.PartnerPaymentConvert;
import net.jinyiyun.admin.partner.support.model.VO.ExportPartnerDateAmountVO;
import net.jinyiyun.admin.partner.support.model.VO.ExportPartnerPaymentVO;
import net.jinyiyun.admin.partner.support.model.VO.PartnerPaymentVO;
import net.jinyiyun.admin.partner.support.model.query.PartnerPaymentPagerQuery;
import net.jinyiyun.admin.partner.support.model.request.ExportPartnerDateAmountRequest;
import net.jinyiyun.admin.partner.support.model.request.PartnerPaymentExcelRequest;
import net.jinyiyun.framework.common.BasePager;
import net.jinyiyun.framework.common.Pager;
import net.jinyiyun.framework.partner.service.PartnerMainService;
import net.jinyiyun.framework.partner.service.PartnerPaymentService;
import net.jinyiyun.framework.partner.service.model.BO.PartnerDateAmountBO;
import net.jinyiyun.framework.partner.service.model.BO.PartnerPaymentBO;
import net.jinyiyun.framework.partner.service.model.query.PartnerDateAmountBOQuery;
import net.jinyiyun.framework.partner.service.model.query.PartnerPaymentBOPagerQuery;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;


@Component
@RequiredArgsConstructor
public class PartnerPaymentSupport {

    private final PartnerPaymentService partnerPaymentService;

    private final PartnerMainService partnerService;

    private static final Integer PAGE_SIZE = 1000;

    /**
     * 分页查询
     */
    public Pager<PartnerPaymentVO> page(PartnerPaymentPagerQuery query) {
        Pager<PartnerPaymentBO> data = partnerPaymentService.pager(PartnerPaymentConvert.toQuery(query));
        if (CollUtil.isEmpty(data.getData())) {
            return new Pager<>(query.getPage(), query.getPageSize(), 0, Lists.newArrayList());
        }
        return new Pager<>(data.getPageNum(),
                data.getPageSize(),
                data.getTotal(),
                PartnerPaymentConvert.convertToVO(data.getData()));
    }

    /**
     * 获取时间范围内的收支明细excel
     */
    public void excel(HttpServletResponse response, PartnerPaymentExcelRequest request) throws Exception {

        List<PartnerPaymentBO> data = Lists.newArrayList();
        // 分批获取时间段内的收支明细
        for (int i = 1; ; i++) {
            PartnerPaymentBOPagerQuery query = PartnerPaymentBOPagerQuery.builder()
                    .createTimeStart(request.getCreateTimeStart())
                    .createTimeEnd(request.getCreateTimeEnd())
                    .pager(new BasePager.Pager(i, PAGE_SIZE))
                    .build();
            List<PartnerPaymentBO> list = partnerPaymentService.pager(query).getData();
            data.addAll(list);
            if (list.size() < PAGE_SIZE) {
                break;
            }
        }

        List<ExportPartnerPaymentVO> list = Lists.newArrayList();

        // 构建excel数据
        //todo 志达 合伙人判空处理
        for (PartnerPaymentBO item : data) {
            list.add(ExportPartnerPaymentVO.builder()
                    .id(item.getId().toString())
                    .title(item.getTitle())
                    .partner(partnerService.getById(item.getPartnerId()).getTitle())
                    .amount(item.getAmount())
                    .currentAmount(item.getCurrentAmount())
                    .moneyType(item.getIsRebate().getDesc())
                    .relationType(item.getRelationType().getDescription())
                    .relationId(item.getRelationId().toString())
                    .createdAt(item.getCreatedAt())
                    .build());
        }

        // 输出excel
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("合伙人收支明细", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        System.out.println(JSONUtil.toJsonStr(response));
        EasyExcel.write(response.getOutputStream(), ExportPartnerPaymentVO.class).sheet("收支明细").doWrite(list);

    }

    /**
     * 导出截止某一天的合伙人余额
     */
    public void exportDatePartnerAmount(ExportPartnerDateAmountRequest request, HttpServletResponse response) throws IOException {
        List<PartnerDateAmountBO> data =
                partnerPaymentService.selectPartnerDateAmount(PartnerDateAmountBOQuery.builder()
                .channelId(request.getChannelId())
                .date(request.getEndTime()).build());
        List<ExportPartnerDateAmountVO> modelList = PartnerPaymentConvert.amount2VO(data);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("合伙人余额", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), ExportPartnerDateAmountVO.class).sheet("合伙人余额").doWrite(modelList);
    }

}
