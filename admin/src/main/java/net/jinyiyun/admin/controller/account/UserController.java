package net.jinyiyun.admin.controller.account;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import net.jinyiyun.common.lib.MbQueryLib;
import net.jinyiyun.config.exception.AlertException;
import net.jinyiyun.config.request.PageReq;
import net.jinyiyun.config.response.Resp;
import net.jinyiyun.framework.entity.User;
import net.jinyiyun.framework.service.UserService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 业务员
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;


    @GetMapping
    @PreAuthorize("hasAuthority('user')")
    public Resp index(PageReq pageDto, User dto) {
        return Resp.success(userService.page(pageDto.toMpPage(), MbQueryLib.getQueryWrapper(dto).eq("type", User.Type.PARTNER.getCode())));
    }


    @PostMapping
    @PreAuthorize("hasAuthority('user:add')")
    @Transactional(rollbackFor = Exception.class)
    public Resp store(@RequestBody @Validated User dto) {
        userService.lambdaQuery().eq(User::getTel, dto.getTel()).oneOpt().ifPresent(u -> {
            throw new AlertException("手机号已存在");
        });
        dto.setPassword(new BCryptPasswordEncoder().encode(dto.getPassword()));
        dto.setType(User.Type.PARTNER.getCode());
        userService.save(dto);
        return Resp.success();
    }


    @PatchMapping("{id}")
    @PreAuthorize("hasAuthority('user:edit')")
    @Transactional(rollbackFor = Exception.class)
    public Resp update(@PathVariable Long id, @RequestBody @Validated User dto) {
        dto.setPassword(StrUtil.isBlank(dto.getPassword()) ? null : new BCryptPasswordEncoder().encode(dto.getPassword()));
        dto.setType(User.Type.PARTNER.getCode());
        userService.updateById(dto);
        return Resp.success();
    }

}
