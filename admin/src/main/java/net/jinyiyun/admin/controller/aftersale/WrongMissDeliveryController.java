/**
 * <AUTHOR>
 * @DATE 2024/6/27
 */
package net.jinyiyun.admin.controller.aftersale;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.jinyiyun.common.lib.MbQueryLib;
import net.jinyiyun.common.security.Auth;
import net.jinyiyun.config.request.PageReq;
import net.jinyiyun.config.response.Resp;
import net.jinyiyun.framework.common.annotation.PreventResubmission;
import net.jinyiyun.framework.common.enums.ResubmissionStrategyEnum;
import net.jinyiyun.framework.dto.BatchRequest;
import net.jinyiyun.framework.dto.aftersale.WrongMissDeliveryDto;
import net.jinyiyun.framework.entity.Admin;
import net.jinyiyun.framework.entity.WrongMissDelivery;
import net.jinyiyun.framework.entity.WrongMissDeliveryGoods;
import net.jinyiyun.framework.service.WrongMissDeliveryService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/6/27
 *
 * 退换交换
 */
@RestController
@RequestMapping("/after/sale/wrongMissDelivery")
@Slf4j
@RequiredArgsConstructor
public class WrongMissDeliveryController {

    private final WrongMissDeliveryService wrongMissDeliveryService;


    /**
     * 获取当前创建人能看的草稿，只有1条
     * @param pageDto
     * @param dto
     * @return
     */
    @GetMapping("/draft")
    public Resp indexDraft(PageReq pageDto, WrongMissDelivery dto ) {
        QueryWrapper<WrongMissDelivery> queryWrapper = MbQueryLib.getQueryWrapper(dto);
        // 草稿状态
        queryWrapper.eq("audit_state",0);
        // 当前创建人
        Admin user = Auth.getUser();
        queryWrapper.eq("creater",user.getId());

        return Resp.success(wrongMissDeliveryService.page(pageDto.toMpPage(), queryWrapper));

    }



    /**
     * 分页查询
     *
     * 根据发货单ID，获取售后单
     * @param pageDto
     * @param dto
     * @return
     */
    @GetMapping
    public Resp index(PageReq pageDto, WrongMissDeliveryDto dto) {
        return Resp.success(wrongMissDeliveryService.index(pageDto,dto));

    }



    @PostMapping
    public Resp store(@RequestBody @Validated WrongMissDeliveryDto dto) {
        Admin user = Auth.getUser();
        dto.setCreater(user.getId());
        wrongMissDeliveryService.store(dto);
        return Resp.success();

    }


    @PatchMapping("/{id}")
    public Resp update(@PathVariable Long id, @RequestBody @Validated WrongMissDeliveryDto dto) {

        dto.setId(id);
        wrongMissDeliveryService.edit(dto);
        return Resp.success();

    }


    /**
     * 提交审核
     * @param id 发货单ID
     * @return
     */
    @GetMapping("/submit/dingding/{id}")
    public Resp submitDing(@PathVariable Long id) {

        wrongMissDeliveryService.submit(id);
        return Resp.success();
    }


    /**
     * 取消审核
     * @param id 发货单ID
     * @return
     */
    @GetMapping("/cancel/submit/dingding/{id}")
    public Resp cancelSubmitDing(@PathVariable Long id) {

        wrongMissDeliveryService.cancelSubmitDing(id);
        return Resp.success();
    }


    /**
     * 审核
     * @param id 发货单ID
     * @return
     */
    @GetMapping("/audit/{id}/{state}")
    @PreventResubmission(
            keyPrefix = "WRONG_MISS_DELIVERY_AUDIT",
            interval = 10,
            timeUnit = TimeUnit.SECONDS,
            strategy = ResubmissionStrategyEnum.ID_METHOD_SIGNATURE,
            idSpEL = "#id"
    )
    public Resp audit(@PathVariable Long id,@PathVariable Integer state) {
        wrongMissDeliveryService.auditPass(id,state);
        return Resp.success();
    }



    @GetMapping("goods/list")
    public Resp goodsIndex(PageReq pageDto, WrongMissDeliveryGoods dto) {
        return Resp.success(wrongMissDeliveryService.goodsIndex(pageDto,dto));

    }

    /**
     * 批量审核
     * 通过/拒绝
     *
     */
    @PostMapping("/batch/audit/{state}")
    public Resp batchAudit(@RequestBody BatchRequest batchRequest, @PathVariable Integer state) {
        wrongMissDeliveryService.batchAuditPass(batchRequest,state);
        return Resp.success();
    }
}
