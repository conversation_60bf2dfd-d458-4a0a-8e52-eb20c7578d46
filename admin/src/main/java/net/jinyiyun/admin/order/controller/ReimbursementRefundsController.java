/**
 * <AUTHOR>
 * @DATE 2024/6/27
 */
package net.jinyiyun.admin.order.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.jinyiyun.common.lib.MbQueryLib;
import net.jinyiyun.common.security.Auth;
import net.jinyiyun.config.request.PageReq;
import net.jinyiyun.config.response.Resp;
import net.jinyiyun.framework.common.annotation.PreventResubmission;
import net.jinyiyun.framework.common.enums.ResubmissionStrategyEnum;
import net.jinyiyun.framework.dto.aftersale.ReimbursementRefundsDto;
import net.jinyiyun.framework.entity.Admin;
import net.jinyiyun.framework.entity.ReimbursementRefunds;
import net.jinyiyun.framework.entity.ReimbursementRefundsGoods;
import net.jinyiyun.framework.service.ReimbursementRefundsService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/6/27
 */
@RestController
@RequestMapping("/after/sale/reimbursementRefunds")
@Slf4j
@RequiredArgsConstructor
public class ReimbursementRefundsController {

    private final ReimbursementRefundsService reimbursementRefundsService;

    /**
     * 获取当前创建人能看的草稿，只有1条
     * @param pageDto
     * @param dto
     * @return
     */
    @GetMapping("/draft")
    public Resp indexDraft(PageReq pageDto, ReimbursementRefundsDto dto ) {
        QueryWrapper<ReimbursementRefunds> queryWrapper = MbQueryLib.getQueryWrapper(dto);
        // 草稿状态
        queryWrapper.eq("audit_state",0);
        // 当前创建人
        Admin user = Auth.getUser();
        queryWrapper.eq("creater",user.getId());
        queryWrapper.eq("delivery_id", dto.getDeliveryId());

        return Resp.success(reimbursementRefundsService.page(pageDto.toMpPage(), queryWrapper));

    }


    /**
     * 分页查询
     *
     * 根据发货单ID，获取售后单
     * @param pageDto
     * @param dto
     * @return
     */
    @GetMapping
    public Resp index(PageReq pageDto, ReimbursementRefundsDto dto ) {
        return Resp.success(reimbursementRefundsService.index(pageDto,dto));

    }



    @PostMapping
    public Resp store(@RequestBody @Validated ReimbursementRefundsDto dto) {
        Admin user = Auth.getUser();
        dto.setCreater(user.getId());
        reimbursementRefundsService.store(dto);
        return Resp.success();

    }


    /**
     * 取消审核
     * @param id 发货单ID
     * @return
     */
    @GetMapping("/cancel/submit/dingding/{id}")
    public Resp cancelSubmitDing(@PathVariable Long id) {

        reimbursementRefundsService.cancelSubmitDing(id);
        return Resp.success();
    }


    /**
     * 审核
     * @param id 发货单ID
     * @return
     */
    @GetMapping("/audit/{id}/{state}")
    @PreventResubmission(
            keyPrefix = "REIMBURSEMENT_REFUNDS_AUDIT",
            interval = 10,
            timeUnit = TimeUnit.SECONDS,
            strategy = ResubmissionStrategyEnum.ID_METHOD_SIGNATURE,
            idSpEL = "#id"
    )
    public Resp audit(@PathVariable Long id,@PathVariable Integer state) {
        reimbursementRefundsService.auditPass(id,state);
        return Resp.success();
    }


    /**
     * 查询售后单下商品列表
     * @param pageDto
     * @param dto
     * @return
     */
    @GetMapping("goods/list")
    public Resp goodsIndex(PageReq pageDto, ReimbursementRefundsGoods dto) {
        return Resp.success(reimbursementRefundsService.goodsIndex(pageDto,dto));

    }
}
