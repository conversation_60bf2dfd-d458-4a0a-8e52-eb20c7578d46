package net.jinyiyun.framework.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import net.jinyiyun.framework.entity.base.BaseEntity;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 订单发货单
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName(value = "order_deliveries")
public class OrderDelivery extends BaseEntity implements Serializable {
    /**
     * 订单ID
     */
    @TableField(value = "order_id")
    private Long orderId;

    /**
     * 是否同步
     */
    @TableField(value = "is_sync")
    private Boolean isSync;

    /**
     * 吉客云单号
     */
    @TableField(value = "jy_no")
    private String jyNo;

    /**
     * 合伙人ID
     */
    @TableField(value = "partner_id")
    private Long partnerId;


    /**
     * 合伙人名称
     */
    @TableField(value = "partner_title")
    private String partnerTitle;

    /**
     * 合伙人渠道
     */
    @TableField(value = "partner_channel")
    private String partnerChannel;


    /**
     * 状态
     * 1010待审核,
     * 1020审核中,
     * 1030预售,
     * 1050待复核,
     * 2000备货等待,
     * 2010备货等待等补货,
     * 2020服务等待,
     * 2030备货等待等生产,
     * 2040采购等待,
     * 3010虚拟发货,
     * 4110待发货待递交,
     * 4111待发货递交中,
     * 4112待发货已递交,
     * 4113待发货-递交失败,
     * 4121待发货-取消中,
     * 4122待发货已取消,
     * 4123待发货取消失败,
     * 4130待发货部分发货,
     * 4040代销发货待递交,
     * 4041代销发货已递交,
     * 5010已取消,
     * 5020已取消被合并,
     * 5030已取消被拆分,
     * 6000发货在途,
     * 9090已完成
     * <p>
     * 归类后:
     * 待审核: 1010,1020
     * 待发货: 1030,1050,2000,2010,2020,2030,2040,3010,4110,4111,4112,4113,4121,4122,4123,4130,4040,4041
     * 已取消: 5010,5020,5030   5010已取消 只有这个才会正式取消 ,5020已取消被合并,5030已取消被拆分
     * 发货在途: 6000
     * 已完成: 9090
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 是否吉客云创建 0.不是吉客云，为分销系统创建1.吉客云创建
     */
    @TableField(value = "is_jky_create")
    private Boolean isJkyCreate;

    /**
     * 收货人
     */
    @TableField(value = "receiver_name")
    private String receiverName;

    /**
     * 收货电话
     */
    @TableField(value = "receiver_phone")
    private String receiverPhone;

    /**
     * 收货手机号
     */
    @TableField(value = "receiver_mobile")
    private String receiverMobile;

    /**
     * 省份
     */
    @TableField(value = "province_title")
    private String provinceTitle;

    /**
     * 城市
     */
    @TableField(value = "city_title")
    private String cityTitle;

    /**
     * 区县
     */
    @TableField(value = "district_title")
    private String districtTitle;

    /**
     * 详细地址
     */
    @TableField(value = "address")
    private String address;

    /**
     * 物流名称
     */
    @TableField(value = "logistic_company")
    private String logisticCompany;

    /**
     * 物流单号
     */
    @TableField(value = "logistic_no")
    private String logisticNo;

    /**
     * 发货时间
     */
    @TableField(value = "delivery_time")
    private LocalDateTime deliveryTime;


    /**
     * 同步错误原因
     */
    @TableField(value = "sync_error_notes")
    private String syncErrorNotes;

    private static final long serialVersionUID = 1L;


    /**
     * 业务员ID
     */
    @TableField(value = "business_id")
    private Long businessId;


    /**
     * 业务员
     */
    @TableField(value = "business_name")
    private String businessName;

    /**
     * 订单交易类型
     * 1-零售业务;9-批发业务B2B
     */
    @TableField(value = "trade_type")
    private Integer tradeType;

    /**
     * 销售渠道
     */
    @TableField(value = "sale_channel")
    private String saleChannel;


    /**
     * 客服备注
     */
    @TableField(value = "custom_notes")
    private String customNotes;


    /**
     * 状态枚举
     * 1010待审核,
     * 1020审核中,
     * 1030预售,
     * 1050待复核,
     * 2000备货等待,
     * 2010备货等待等补货,
     * 2020服务等待,
     * 2030备货等待等生产,
     * 2040采购等待,
     * 3010虚拟发货,
     * 4110待发货待递交,
     * 4111待发货递交中,
     * 4112待发货已递交,
     * 4113待发货-递交失败,
     * 4121待发货-取消中,
     * 4122待发货已取消,
     * 4123待发货取消失败,
     * 4130待发货部分发货,
     * 4040代销发货待递交,
     * 4041代销发货已递交,
     * 5010已取消,
     * 5020已取消被合并,
     * 5030已取消被拆分,
     * 6000发货在途,
     * 9090已完成
     */
    public enum State {
        /**
         * 1010待审核,
         * 1020审核中,
         * 1030预售,
         * 1050待复核,
         * 2000备货等待,
         * 2010备货等待等补货,
         * 2020服务等待,
         * 2030备货等待等生产,
         * 2040采购等待,
         * 3010虚拟发货,
         * 4110待发货待递交,
         * 4111待发货递交中,
         * 4112待发货已递交,
         * 4113待发货-递交失败,
         * 4121待发货-取消中,
         * 4122待发货已取消,
         * 4123待发货取消失败,
         * 4130待发货部分发货,
         * 4040代销发货待递交,
         * 4041代销发货已递交,
         * 5010已取消,
         * 5020已取消被合并,
         * 5030已取消被拆分,
         * 6000发货在途,
         * 9090已完成
         * <p>
         * 归类后:
         * 待审核: 1010,1020
         * 待发货: 1030,1050,2000,2010,2020,2030,2040,3010,4110,4111,4112,4113,4121,4122,4123,4130,4040,4041
         * 已取消: 5010,5020,5030   5010已取消 只有这个才会正式取消 ,5020已取消被合并,5030已取消被拆分
         * 发货在途: 6000
         * 已完成: 9090
         */
        WAIT_AUDIT(1010, "待审核"),
        AUDITING(1020, "审核中"),
        PRE_SALE(1030, "预售"),
        WAIT_REVIEW(1050, "待复核"),
        WAIT_STOCK(2000, "备货等待"),
        WAIT_STOCK_REPLENISHMENT(2010, "备货等待等补货"),
        WAIT_SERVICE(2020, "服务等待"),
        WAIT_STOCK_PRODUCTION(2030, "备货等待等生产"),
        WAIT_PURCHASE(2040, "采购等待"),
        VIRTUAL_DELIVERY(3010, "虚拟发货"),
        WAIT_DELIVERY(4110, "待发货待递交"),
        DELIVERY_ING(4111, "待发货递交中"),
        DELIVERY_ED(4112, "待发货已递交"),
        DELIVERY_FAIL(4113, "待发货-递交失败"),
        DELIVERY_CANCELING(4121, "待发货-取消中"),
        DELIVERY_CANCELED(4122, "待发货已取消"),
        CANCEL_FAIL(4123, "待发货取消失败"),
        PARTIAL_SHIPMENT(4130, "待发货部分发货"),
        CONSIGNMENT_SHIPPING_TO_BE_SUBMITTED(4040, "代销发货待递交"),
        CONSIGNMENT_SHIPPING_SUBMITTED(4041, "代销发货已递交"),
        CANCELED(5010, "已取消"),
        CANCELED_AND_MERGED(5020, "已取消被合并"),
        CANCELED_AND_SPLIT(5030, "已取消被拆分"),
        DELIVERY_ON_THE_WAY(6000, "发货在途"),
        COMPLETED(9090, "已完成");

        private final Integer code;
        private final String desc;

        State(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static State getEnum(Integer code) {
            for (State state : State.values()) {
                if (state.getCode().equals(code)) {
                    return state;
                }
            }

            throw new IllegalArgumentException("无效的发货单状态");
        }

        /**
         * 待收货/已发货状态集合
         *
         * @return Set<State>
         */
        public static List<State> getDeliveryState() {
            return Arrays.asList(
                    WAIT_REVIEW,
                    WAIT_STOCK,
                    WAIT_STOCK_REPLENISHMENT,
                    WAIT_SERVICE,
                    WAIT_STOCK_PRODUCTION,
                    WAIT_PURCHASE,
                    VIRTUAL_DELIVERY,
                    WAIT_DELIVERY,
                    DELIVERY_ING,
                    DELIVERY_ED,
                    DELIVERY_FAIL,
                    DELIVERY_CANCELING,
                    DELIVERY_CANCELED,
                    CANCEL_FAIL,
                    PARTIAL_SHIPMENT,
                    CONSIGNMENT_SHIPPING_TO_BE_SUBMITTED,
                    CONSIGNMENT_SHIPPING_SUBMITTED,
                    DELIVERY_ON_THE_WAY
            );
        }

        /**
         * 已取消状态集合
         */
        public static List<State> getCanceledState() {
            return Arrays.asList(
                    CANCELED,
                    CANCELED_AND_MERGED,
                    CANCELED_AND_SPLIT
            );
        }


        /**
         * 订单全部已发货状态集合
         * 含 已完成,已取消集合
         */
        public static List<State> getCompleted() {
            List<State> allState = new ArrayList<>(getCanceledState());
            allState.add(COMPLETED);
            return allState;
        }


        /**
         * 待发货状态集合
         */
        public static List<State> getWaitDeliveryState() {
            List<State> allState = new ArrayList<>(getCanceledState());
            allState.add(WAIT_AUDIT);
            return allState;
        }
    }

}
