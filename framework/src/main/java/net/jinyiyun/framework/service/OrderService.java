package net.jinyiyun.framework.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SimpleQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.jinyiyun.common.constant.SqlConstants;
import net.jinyiyun.common.emums.RuleRebateTypeEnum;
import net.jinyiyun.common.emums.RuleTriggerAmountTypeEnum;
import net.jinyiyun.common.emums.RuleTriggerConditionEnum;
import net.jinyiyun.common.emums.RuleTriggerFrequencyEnum;
import net.jinyiyun.common.lib.MbQueryLib;
import net.jinyiyun.common.security.Auth;
import net.jinyiyun.common.utils.BigDecimalUtil;
import net.jinyiyun.common.utils.JacksonUtil;
import net.jinyiyun.config.exception.AlertException;
import net.jinyiyun.config.request.PageReq;
import net.jinyiyun.config.resolver.datetimerange.LocalDateTimeRange;
import net.jinyiyun.config.response.Resp;
import net.jinyiyun.framework.common.Response;
import net.jinyiyun.framework.dto.GoodsSpecBo;
import net.jinyiyun.framework.dto.OrderAllInfoResp;
import net.jinyiyun.framework.dto.OrderDeliveryBo;
import net.jinyiyun.framework.dto.OrderGiveGoodsBo;
import net.jinyiyun.framework.dto.admin.GroupsBo;
import net.jinyiyun.framework.dto.admin.OrdersDto;
import net.jinyiyun.framework.dto.aftersale.ExchangeDeliveryDto;
import net.jinyiyun.framework.dto.aftersale.ReimbursementRefundsDto;
import net.jinyiyun.framework.dto.aftersale.WrongMissDeliveryDto;
import net.jinyiyun.framework.dto.app.RulePriceSystemDetailBo;
import net.jinyiyun.framework.dto.app.businessbatchaddress.OrderBatchSubmitReq;
import net.jinyiyun.framework.dto.app.order.*;
import net.jinyiyun.framework.dto.invoiceapplication.OrderInvoiceAmount;
import net.jinyiyun.framework.entity.*;
import net.jinyiyun.framework.entity.base.BaseEntity;
import net.jinyiyun.framework.mapper.OrderGoodsMapper;
import net.jinyiyun.framework.mapper.OrderMapper;
import net.jinyiyun.framework.mapper.RebateApplicationMapper;
import net.jinyiyun.framework.mapper.ShoppingCartMapper;
import net.jinyiyun.framework.order.mapper.ExchangeDeliveryMapper;
import net.jinyiyun.framework.order.mapper.ReimbursementRefundsMapper;
import net.jinyiyun.framework.order.mapper.WrongMissDeliveryMapper;
import net.jinyiyun.framework.query.OrdersQuery;
import net.jinyiyun.framework.service.hryun.HrYunService;
import net.jinyiyun.framework.service.jackyun.JackYunService;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static net.jinyiyun.framework.entity.OrderDelivery.State.getCompleted;

/**
 * <AUTHOR>
 */
@Lazy
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderService extends ServiceImpl<OrderMapper, Order> {

    /**
     * 商品满赠
     */
    private final RuleGoodsGiveService ruleGoodsGiveService;

    /**
     * 订单满赠
     */
    private final RuleOrderGiveService ruleOrderGiveService;

    /**
     * 订单返利
     */
    private final RuleOrderRebateService ruleOrderRebateService;

    /**
     * 价格体系
     */
    private final RulePriceSystemService rulePriceSystemService;

    /**
     * 订单促销
     */
    private final RuleOrderPromotionService ruleOrderPromotionService;

    /**
     * 会员等级
     */
    private final LevelService levelService;


    /**
     * 订单缓存
     */
    private final OrderCacheService orderCacheService;


    /**
     * 商品
     */
    private final GoodsService goodsService;

    /**
     * 商品规格
     */
    private final GoodsSpecService goodsSpecService;


    /**
     * 赠品
     */
    private final GiveGoodsService giveGoodsService;

    /**
     * 促销品
     */
    private final PromotionGoodsService promotionGoodsService;

    /**
     * 收货地址
     */
    private final PartnerAddressService addressService;

    /**
     * 合伙人
     */
    private final PartnerService partnerService;

    /**
     * 订单下商品
     */
    private final OrderGoodsService orderGoodsService;
    /**
     * 订单下赠品
     */
    private final OrderGiveGoodsService orderGiveGoodsService;

    /**
     * 订单下返利
     */
    private final OrderRebateService orderRebateService;
    /**
     * 订单下促销
     */
    private final OrderPromotionGoodsService orderPromotionGoodsService;


    /**
     * 购物车
     */
    private final ShoppingCartService shoppingCartService;


    /**
     * 发货单
     */
    private final OrderDeliveryService orderDeliveryService;

    /**
     * 渠道
     */
    private final ChannelService channelService;

    /**
     * 吉客云
     */
    private final JackYunService jackYunService;

    /**
     * 用户
     */
    private final UserService userService;


    private final OrderMapper orderMapper;

    /**
     * 赠品的配置
     */
    private final GiveConfigService giveConfigService;

    private final RuleRelationGiveGoodService relationGiveGoodService;

    private final RuleRelationGoodsSpecService ruleRelationGoodsSpecService;

    /**
     * 组合
     */
    private final GroupService groupService;

    /**
     * 组合满赠
     */
    private final RuleGroupGivesService ruleGroupGivesService;

    /**
     * 规则关联的组合
     */
    private final RuleRelationGroupGoodsService ruleRelationGroupGoodsService;


    private final RebateApplicationMapper rebateApplicationMapper;

    private final HrYunService hrYunService;

    private final ReimbursementRefundsService reimbursementRefundsService;
    private final ExchangeDeliveryService exchangeDeliveryService;
    private final WrongMissDeliveryService wrongMissDeliveryService;
    private final OrderGoodsMapper orderGoodsMapper;
    private final ShoppingCartMapper shoppingCartMapper;
    private final ReimbursementRefundsMapper reimbursementRefundsMapper;
    private final ExchangeDeliveryMapper exchangeDeliveryMapper;
    private final WrongMissDeliveryMapper wrongMissDeliveryMapper;

    private final TransactionTemplate transactionTemplate;

    /**
     * 确认订单
     *
     * @param confirmReq OrderConfirmReq
     * @param userId     下单用户id
     * @param partner    合伙人
     * @return OrderConfirmResp
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderConfirmResp orderConfirm(OrderConfirmReq confirmReq, Long userId, Partner partner) {

        // 查询合伙人渠道
        if (channelService.getById(partner.getChannelId()) == null) {
            throw new AlertException("合伙人渠道不存在!请联系管理员绑定渠道!");
        }

        // 构建规格和数量map [规格id=>数量]
        Map<Long, Integer> goodsSpecMap = confirmReq.getGoodsItems().stream().collect(Collectors.toMap(GoodsItemReq::getSpecId, GoodsItemReq::getNum));

        // 初始化订单
        Order order = new Order();
        order.setId(IdWorker.getId());
        order.setPartnerId(partner.getId());
        order.setUserId(userId);
        order.setState(Order.State.WAIT_PAY.getCode());
        OrderConfirmResp resp = new OrderConfirmResp();


        // 合伙人折扣
        BigDecimal partnerLevelDiscount = levelService.getDiscount(partner.getLevelId());
        log.info("confirm 合伙人折扣:{}", partnerLevelDiscount);

        // 价格体系
        Map<Long, RulePriceSystemDetailBo> specIdPriceSystems = rulePriceSystemService.filterSelectPrice(rulePriceSystemService.getAllPriceSystem(partner.getId(), goodsSpecMap), goodsSpecMap, partnerLevelDiscount);
        log.info("confirm 价格体系:{}", specIdPriceSystems);

        //1. 计算单件商品价格
        // 获取订单下商品  应付金额 价格来源[折扣,价格体系]
        List<OrderGoods> orderGoods = getOrderGoods(order, goodsSpecMap, partnerLevelDiscount, specIdPriceSystems);

        //2. 计算订单全额（返利支付、实际支付）
        // 订单估计商品金额
        setEstimateGoodsAmount(order, orderGoods, partner, specIdPriceSystems);
        log.info("confirm 订单估计:{}", order);

        //3. 根据返利金额计算每件商品价格
        // 订单实际商品金额
        setRealGoodsAmount(order, orderGoods, partner);
        log.info("confirm 订单实际:{}", order);

        //4. 获取组合满赠商品
        // 组合满赠，排除掉商品是属于普通类型的
        List<OrderGoods> orderGoodsListGroup = orderGoods.stream().filter(x -> x.getType().intValue() == 1).collect(Collectors.toList());
        getGiveGoodsGroupType(resp,order, orderGoodsListGroup, partner);

        //5. 获取商品满赠商品
        // 规则 商品满赠 ，每个商品都会赠送, 排除掉商品是属于组合类型的
        List<OrderGoods> orderGoodsListExclueGroup = orderGoods.stream().filter(x -> x.getType().intValue() == 0).collect(Collectors.toList());
        List<OrderGiveGoods> orderGiveGoodsByGoods = getOrderGiveGoodsByGoods(order, orderGoodsListExclueGroup, partner);
        log.info("confirm 商品满赠:{}", orderGiveGoodsByGoods);

        //6. 处理订单满赠
        // 规则 订单满赠， 整个订单只赠送一次
        getOrderGiveGoodsByOrder(resp,order, orderGoods, partner);

        // 规则 订单返利
        OrderRebate orderRebate = getOrderRebate(order, orderGoods, partner);
        log.info("confirm 订单返利:{}", orderRebate);

        // 规则 订单满促 —— 只能享受一次
        RuleOrderPromotionBo ruleOrderPromotionBo = ruleOrderPromotionService.getOneByOrder(order, orderGoods, partnerLevelDiscount);
        log.info("confirm 订单满促:{}", ruleOrderPromotionBo);

        // 设置预订单信息
        resp.setOrder(order);
        // 订单下所有全量商品，没有区分普通/组合
        resp.setOrderGoods(orderGoods);
        // 商品满赠固定
        resp.setGoodsGives(orderGiveGoodsByGoods);
        resp.setOrderRebate(orderRebate);
        resp.setRuleOrderPromotion(ruleOrderPromotionBo);

        resp.setOrderPromotionUpperLimitRebateAmount(partner.getRebateAmount().subtract(order.getPayRebateAmount()));
        resp.setOrderPromotionUpperLimitBalanceAmount(partner.getAmount().subtract(order.getPayBalanceAmount()));

        // 处理组合下商品和普通商品
        handlerGroup(resp,orderGoods);

        // 存入 订单缓存表
        orderCacheService.save(resp);
        return resp;
    }

    public void handlerGroup(OrderConfirmResp resp, List<OrderGoods> orderGoods) {
        // 订单下普通商品
        List<OrderGoods> ordinary = orderGoods.stream().filter(x -> x.getType().intValue() == 0).collect(Collectors.toList());
        resp.setOrderOrdinaryGoods(ordinary);


        ArrayList<GroupsBo> groupsBos = new ArrayList<>();

        // 订单下组合商品
        Map<Long, List<OrderGoods>> collect = orderGoods.stream().filter(x -> x.getType().intValue() == 1).collect(Collectors.groupingBy(x -> x.getGroupId()));
        for (Map.Entry<Long, List<OrderGoods>> entry : collect.entrySet()) {
            Long groupId = entry.getKey();
            Groups groups = groupService.getById(groupId);
            GroupsBo groupsBo = new GroupsBo();
            BeanUtil.copyProperties(groups,groupsBo);
            groupsBo.setGoods(entry.getValue());
            groupsBos.add(groupsBo);
        }
        resp.setOrderGroupsGoods(groupsBos);

    }


    /**
     * 校验订单是否可提交
     */
    @Transactional(rollbackFor = Exception.class)
    public Resp check(OrderCheckReq checkReq, Long userId, Partner partner) {
        // 订单重复校验
        lambdaQuery().eq(Order::getId, checkReq.getOrderId()).oneOpt().ifPresent(order -> {
            throw new AlertException("订单重复提交!");
        });
        // 促销品
        OrderConfirmResp confirm = orderCacheService.get(checkReq.getOrderId(), userId);
        // 促销品下单处理 校验是否超卖 处理金额
        List<OrderPromotionGoods> orderPromotionGoodsList = getOrderPromotionGoods(confirm, checkReq.getPromotionGoodsItems(), partner);
        // 设置促销金额金额
        setEstimatePromotionAmount(confirm.getOrder(), orderPromotionGoodsList);
        // 校验返利金额
        BigDecimal after = partner.getRebateAmount().subtract(confirm.getOrder().getPayRebateAmount());
        // 判断返利余额是否充足
        if (after.compareTo(BigDecimal.ZERO) < 0) {
            throw new AlertException("返利余额不足!");
        }
        // 变化后余额
        after = partner.getAmount().subtract(confirm.getOrder().getPayBalanceAmount());
        // 有信用额度, 判断是否充足
        if (after.add(partner.getCreditAmount()).compareTo(BigDecimal.ZERO) < 0) {
            throw new AlertException("余额不足!");
        }
        return Resp.success();
    }


    /**
     * 提交订单
     *
     * @param submitReq OrderSubmitReq
     * @param userId    下单用户id
     * @param partner   合伙人
     */
    @Transactional(rollbackFor = Exception.class)
    public Order submit(OrderSubmitReq submitReq, Long userId, Partner partner) {
        // 获取合伙人渠道
        Channel channel = Optional.ofNullable(channelService.getById(partner.getChannelId())).orElseThrow(() -> new AlertException("合伙人渠道不存在!"));
        OrderConfirmResp confirm = orderCacheService.get(submitReq.getOrderId(), userId);
        // 处理额外的订单备注，业务员等
        handlerExt(confirm,partner,submitReq.getTradeType(),submitReq.getOrderNotes());
        submitCommon(confirm.getOrder().getId(), submitReq.getGiveGoodsFromOrderItemReqs(), submitReq.getPromotionGoodsItems(),partner, confirm,submitReq.getGroupGiveItemReqs());
        // 删除购物车
        if (submitReq.getIsShoppingCart()) {
            shoppingCartService.remove(userId, confirm.getOrderGoods().stream().map(OrderGoods::getGoodsSpecId).collect(Collectors.toSet()));
        }
        // 生成发货单(商品，赠品，促销品的发货都在这里控制)
        orderDeliveryService.createOneByOrder(getOrderAllInfoById(confirm.getOrder().getId()),
                addressService.getByIdAndPartnerId(submitReq.getAddressId(), partner.getId()),
                partner,
                channel.getTitle(), confirm,submitReq.getTradeType());

        return confirm.getOrder();
    }


    /**
     * 提交订单 公共部分
     *
     * @param orderId             订单id
     * @param giveGoodsFromOrderItemReqs     任选赠品
     * @param partner             合伙人
     * @param confirm             订单确认信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitCommon(Long orderId, List<GiveGoodsItemReq> giveGoodsFromOrderItemReqs,List<PromotionGoodsItemReq> promotionGoodsItems, Partner partner, OrderConfirmResp confirm,List<GroupGiveItemReq>  groupGiveItemReqs) {
        // 订单重复校验
        lambdaQuery().eq(Order::getId, orderId).oneOpt().ifPresent(order -> {
            throw new AlertException("订单重复提交!");
        });
        // 促销品选择
        // 促销品下单处理 校验是否超卖 处理金额
        List<OrderPromotionGoods> orderPromotionGoodsList = getOrderPromotionGoods(confirm, promotionGoodsItems, partner);
        log.info("submit 促销品:{}", JacksonUtil.obj2strDelBlank(orderPromotionGoodsList));

        // 所有赠品
        List<OrderGiveGoods> allGiveList = new ArrayList<>();
        // 商品满赠加进去
        allGiveList.addAll(confirm.getGoodsGives());

        // 订单满赠任选，将订单任选的赠品同样加入到orderGoodsGives
        if (confirm.getRuleOrderGiveBo()!= null && confirm.getRuleOrderGiveBo().getSelectGive() == 1) {
            // 用户提交的订单下赠品，不能超过最大赠品限制金额，和最大数量
            checkOrderGiveAmountAndNum(confirm.getRuleOrderGiveBo(),giveGoodsFromOrderItemReqs);
            // 任选的订单赠品，得加入到orderGoodsGives表
            ArrayList<OrderGiveGoods> orderFreeGiveGoods = new ArrayList<>();
            if (CollUtil.isNotEmpty(giveGoodsFromOrderItemReqs)) {
                for (GiveGoodsItemReq giveGoodsItemReq : giveGoodsFromOrderItemReqs) {
                    OrderGiveGoods orderGiveGoodsEntity = new OrderGiveGoods();
                    // 用户选择的赠品
                    Long giveGoodsId = giveGoodsItemReq.getGiveGoodsId();
                    // 用户选择赠品的数量
                    Integer num = giveGoodsItemReq.getNum();
                    GiveGoods giveGoods = giveGoodsService.getById(giveGoodsId);
                    // 把用户下单的GiveGoodsItemReq 转化为OrderGiveGoods
                    orderGiveGoodsEntity.setOrderId(confirm.getOrder().getId());
                    orderGiveGoodsEntity.setGiveGoodsId(giveGoodsId);
                    orderGiveGoodsEntity.setGiveGoodsTitle(giveGoods.getTitle());
                    orderGiveGoodsEntity.setGiveGoodsBarcode(giveGoods.getBarcode());
                    orderGiveGoodsEntity.setGiveGoodsNo(giveGoods.getNo());
                    orderGiveGoodsEntity.setNumber(num);
                    // 订单满赠 =1
                    orderGiveGoodsEntity.setType(1);
                    orderGiveGoodsEntity.setPrice(giveGoodsItemReq.getPrice());
                    // 订单满赠规则
                    orderGiveGoodsEntity.setRuleOrderGiveId(confirm.getRuleOrderGiveBo().getId());
                    orderFreeGiveGoods.add(orderGiveGoodsEntity);
                }
            }
            // 把订单满赠任选客户选择的赠品，加入到赠品列表
            allGiveList.addAll(orderFreeGiveGoods);
        }else {
            // 订单满赠固定
            if (CollUtil.isNotEmpty(confirm.getOrderGives())) {
                allGiveList.addAll(confirm.getOrderGives());
            }
        }

        // 组合满赠固定，将赠品同样加入到orderGoodsGives
        if (CollUtil.isNotEmpty(confirm.getGroupGiveFixList())) {
            // 组合下固定赠品
            confirm.getGroupGiveFixList().stream().forEach(x->allGiveList.addAll(x.getGroupGives()));
        }
        // 组合满赠任选
        if (CollUtil.isNotEmpty(groupGiveItemReqs)) {
            for (GroupGiveItemReq groupGiveItemReq : groupGiveItemReqs) {

                List<GiveGoodsItemReq> gives = groupGiveItemReq.getGives();
                for (GiveGoodsItemReq give : gives) {
                    OrderGiveGoods orderGiveGoods = new OrderGiveGoods();
                    orderGiveGoods.setOrderId(orderId);
                    Long giveGoodsId = give.getGiveGoodsId();
                    GiveGoods giveGoods = giveGoodsService.getById(giveGoodsId);
                    orderGiveGoods.setGiveGoodsId(giveGoods.getId());
                    orderGiveGoods.setGiveGoodsTitle(giveGoods.getTitle());
                    orderGiveGoods.setGiveGoodsBarcode(giveGoods.getBarcode());
                    orderGiveGoods.setGiveGoodsNo(giveGoods.getNo());
                    orderGiveGoods.setNumber(give.getNum());
                    // 组合满赠规则ID
                    orderGiveGoods.setRuleGroupGiveId(groupGiveItemReq.getRuleGroupGiveId());
                    // 哪一个组合
                    orderGiveGoods.setGroupId(groupGiveItemReq.getGroupId());
                    // 组合满赠类型
                    orderGiveGoods.setType(2);
                    orderGiveGoods.setId(IdWorker.getId());
                    orderGiveGoods.setPrice(give.getPrice());
                    allGiveList.add(orderGiveGoods);
                }
            }
        }

        // 订单金额
        setEstimatePromotionAmount(confirm.getOrder(), orderPromotionGoodsList);
        // 创建订单 (保存全部数据)
        save(confirm.getOrder(), confirm.getOrderGoods(), allGiveList, orderPromotionGoodsList, confirm.getOrderRebate());
    }

    /**
     * 用户提交的订单下赠品，不能超过最大赠品限制金额，和最大数量
     *
     * @param ruleOrderGiveBo
     * @param giveGoodsFromOrderItemReqs
     */
    public void checkOrderGiveAmountAndNum(RuleOrderGiveBo ruleOrderGiveBo, List<GiveGoodsItemReq> giveGoodsFromOrderItemReqs) {
        // 是否有赠品
        boolean flag1 = CollUtil.isNotEmpty(giveGoodsFromOrderItemReqs);
        // 是否，数量，金额为0
        boolean flag2 = ruleOrderGiveBo.getMaxGiveAmount().equals(BigDecimal.ZERO) || ruleOrderGiveBo.getMaxGiveNumber() == 0;
        // todo 这里感觉并没有判断选择的赠品是否超过最大赠品限制，就只判断了订单金额和数量是否为0，并没有比较订单金额和数量，是否超过最大赠品限制，和最大数量限制（若没问题可删）
        if (flag1 && flag2) {
            throw new AlertException("赠品的累计不能超过最大限制");
        }


    }


    /**
     * 批量地址提交订单
     *
     * @param batchSubmitReq 批量地址提交订单请求
     * @param userId         用户id
     * @param partner        合伙人
     * @return 订单
     */
    @Transactional(rollbackFor = Exception.class)
    public Order batchAddressSubmitAndPay(OrderBatchSubmitReq batchSubmitReq, Long userId, Partner partner) {
        // 获取合伙人渠道
        Channel channel = Optional.ofNullable(channelService.getById(partner.getChannelId())).orElseThrow(() -> new AlertException("合伙人渠道不存在!"));
        OrderConfirmResp confirm = orderCacheService.get(batchSubmitReq.getOrderId(), userId);
        // 处理业务员
        handlerExt(confirm,partner,batchSubmitReq.getTradeType(),batchSubmitReq.getOrderNotes());
        submitCommon(confirm.getOrder().getId(), batchSubmitReq.getGiveGoodsItemReqs(),batchSubmitReq.getPromotionGoodsItems(), partner, confirm,batchSubmitReq.getGroupGiveItemReqs());
        // 批量生成发货单
        orderDeliveryService.createBatchByOrder(getOrderAllInfoById(confirm.getOrder().getId()), batchSubmitReq.getOrderBatchSubmitItemReq(), partner, channel.getTitle());
        // 支付订单
        pay(confirm.getOrder());

        return confirm.getOrder();
    }


    /**
     * 支付订单
     *
     * @param order 订单
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean pay(Order order) {
        // 校验订单状态
        if (!order.getState().equals(Order.State.WAIT_PAY.getCode())) {
            throw new AlertException("订单状态异常!");
        }
        // 扣合伙人钱 返利,余额
        if (order.getPayRebateAmount().compareTo(BigDecimal.ZERO) > 0) {
            partnerService.reduceRebateBalance(order.getPartnerId(), order.getPayRebateAmount(), "返利支付订单", Order.class, PartnerPayment.RelationType.REBATE_TRADE_EXPENDITURE, order.getId(),order.getId(),null);
        }
        if (order.getPayBalanceAmount().compareTo(BigDecimal.ZERO) > 0) {
            partnerService.reduceBalance(order.getPartnerId(), order.getPayBalanceAmount(), "余额支付订单", Order.class, PartnerPayment.RelationType.BALANCE_TRADE_EXPENDITURE, order.getId(),order.getId(),null);
        }
        // 更新订单
        boolean update = lambdaUpdate()
                .eq(Order::getId, order.getId())
                .set(Order::getState, Order.State.WAIT_SHIP.getCode())
                .set(Order::getPayTime, LocalDateTime.now())
                .update(new Order());

        if (!update) {
            throw new AlertException("订单支付异常!");
        }
        return true;
    }


    /**
     * 保存订单
     *
     * @param order                   订单
     * @param orderGoodsList          订单商品
     * @param orderGoodsGiveList      订单赠品
     * @param orderPromotionGoodsList 订单促销品
     * @param orderRebate             订单返利
     */
    private void save(Order order, List<OrderGoods> orderGoodsList, List<OrderGiveGoods> orderGoodsGiveList, List<OrderPromotionGoods> orderPromotionGoodsList, OrderRebate orderRebate) {
        order.setState(Order.State.WAIT_PAY.getCode());
        // 保存订单
        save(order);
        // 保存订单商品
        orderGoodsService.saveBatch(orderGoodsList);
        // 保存订单赠品
        if (CollUtil.isNotEmpty(orderGoodsGiveList)) {
            orderGiveGoodsService.saveBatch(orderGoodsGiveList);
        }

        // 保存订单促销品
        if (CollUtil.isNotEmpty(orderPromotionGoodsList)) {
            log.info("save 促销品:{}", JacksonUtil.obj2strDelBlank(orderPromotionGoodsList));
            orderPromotionGoodsService.saveBatch(orderPromotionGoodsList);
        }
        // 保存订单返利
        if (orderRebate != null) {
            orderRebateService.save(orderRebate);
        }
    }


    /**
     * 设置促销金额
     *
     * @param order                   订单
     * @param orderPromotionGoodsList 促销品
     */
    private void setEstimatePromotionAmount(Order order, List<OrderPromotionGoods> orderPromotionGoodsList) {
        if (CollUtil.isEmpty(orderPromotionGoodsList)) {
            return;
        }
        // 促销金额数据 原价 折扣金额 应付 返利支付 余额支付
        BigDecimal promotionOriginalAmount = BigDecimal.ZERO;
        BigDecimal promotionDiscountAmount = BigDecimal.ZERO;
        BigDecimal promotionPayAmount = BigDecimal.ZERO;
        BigDecimal promotionPayRebateAmount = BigDecimal.ZERO;
        BigDecimal promotionPayBalanceAmount = BigDecimal.ZERO;
        for (OrderPromotionGoods opg : orderPromotionGoodsList) {
            BigDecimal number = BigDecimal.valueOf(opg.getNumber());
            promotionOriginalAmount = promotionOriginalAmount.add(BigDecimalUtil.multiply(opg.getOriginalAmount(), number));
            promotionDiscountAmount = promotionDiscountAmount.add(BigDecimalUtil.multiply(opg.getDiscountAmount(), number));
            promotionPayAmount = promotionPayAmount.add(BigDecimalUtil.multiply(opg.getPayAmount(), number));
            promotionPayRebateAmount = promotionPayRebateAmount.add(BigDecimalUtil.multiply(opg.getPayRebateAmount(), number));
            promotionPayBalanceAmount = promotionPayBalanceAmount.add(BigDecimalUtil.multiply(opg.getPayBalanceAmount(), number));
        }
        order.setOriginalAmount(order.getGoodsOriginalAmount().add(promotionOriginalAmount));
        order.setDiscountAmount(order.getGoodsDiscountAmount().add(promotionDiscountAmount));
        order.setPayAmount(order.getGoodsPayAmount().add(promotionPayAmount));
        order.setPayRebateAmount(order.getPayRebateAmount().add(promotionPayRebateAmount));
        order.setPayBalanceAmount(order.getPayBalanceAmount().add(promotionPayBalanceAmount));
    }


    /**
     * 促销品下单处理 校验是否超卖 处理金额
     *
     * @param confirm             确认订单信息
     * @param promotionGoodsItems 促销品
     * @param partner             合伙人
     */
    private List<OrderPromotionGoods> getOrderPromotionGoods(OrderConfirmResp confirm, List<PromotionGoodsItemReq> promotionGoodsItems, Partner partner) {
        // 订单下促销品
        List<OrderPromotionGoods> orderPromotionGoodsList = new ArrayList<>();
        // 没下单促销品
        if (CollUtil.isEmpty(promotionGoodsItems)) {
            return orderPromotionGoodsList;
        }
        Order order = confirm.getOrder();

        // 返利支付比例
        BigDecimal rebatePayProportion = partnerService.getRebatePayProportion(partner);

        // 促销规则
        RuleOrderPromotionBo ruleOrderPromotionBo = confirm.getRuleOrderPromotion();
        if (ruleOrderPromotionBo == null) {
            throw new AlertException("您没有可购买的促销品!");
        }


        // 促销规则 根据促销品ID进行分组,每个分组只有一个
        Map<Long, RuleOrderPromotionGoodsBo> ruleOrderPromotionItems = SimpleQuery.list2Map(ruleOrderPromotionBo.getItems(), RuleOrderPromotionGoodsBo::getPromotionGoodsId, v -> v);
        // 促销品累加数量
        BigDecimal promotionGoodsSumPayPrice = BigDecimal.ZERO;
        for (PromotionGoodsItemReq item : promotionGoodsItems) {
            RuleOrderPromotionGoodsBo promotionGoodsBo = ruleOrderPromotionItems.get(item.getPromotionGoodsId());
            if (promotionGoodsBo == null) {
                throw new AlertException("促销品选择错误,请重新选择");
            }
            // 每单最大购买数量
            Integer maxBuyNum = promotionGoodsBo.getNumberLimit();
            // 购买数量
            Integer buyNum = item.getNum();
            // 超过最大购买数量
            if (maxBuyNum != null && buyNum > maxBuyNum) {
                throw new AlertException("促销品'{}'超过最大购买数量,最多可购买{}个", promotionGoodsBo.getTitle(), maxBuyNum);
            }
            // 该促销品累计金额
            BigDecimal payPrice = BigDecimalUtil.multiply(promotionGoodsBo.getPayPrice(), BigDecimal.valueOf(buyNum));
            if (promotionGoodsBo.getPriceLimit() != null && payPrice.compareTo(promotionGoodsBo.getPriceLimit()) > 0) {
                throw new AlertException("促销品'{}'超过最大购买金额{},已购买{}", promotionGoodsBo.getTitle(), promotionGoodsBo.getPriceLimit(), payPrice);
            }
            promotionGoodsSumPayPrice = promotionGoodsSumPayPrice.add(payPrice);
            OrderPromotionGoods orderPromotionGoods = new OrderPromotionGoods();
            orderPromotionGoods.setOrderId(order.getId());
            orderPromotionGoods.setPromotionGoodsId(promotionGoodsBo.getPromotionGoodsId());
            orderPromotionGoods.setPromotionGoodsTitle(promotionGoodsBo.getTitle());
            orderPromotionGoods.setPromotionGoodsNo(promotionGoodsBo.getNo());
            orderPromotionGoods.setPromotionGoodsBarcode(promotionGoodsBo.getBarcode());
            orderPromotionGoods.setRuleOrderPromotionId(ruleOrderPromotionBo.getId());
            orderPromotionGoods.setOriginalAmount(promotionGoodsBo.getPrice());
            orderPromotionGoods.setDiscountAmount(promotionGoodsBo.getPrice().subtract(promotionGoodsBo.getPayPrice()));
            orderPromotionGoods.setPayAmount(promotionGoodsBo.getPayPrice());
            // 是否允许返利支付
            if (ruleOrderPromotionBo.getIsRebate()) {
                BigDecimal payRebateAmount = BigDecimalUtil.getPercentPrice(promotionGoodsBo.getPayPrice(), rebatePayProportion);
                orderPromotionGoods.setPayRebateAmount(payRebateAmount);
                orderPromotionGoods.setPayBalanceAmount(promotionGoodsBo.getPayPrice().subtract(payRebateAmount));
            } else {
                orderPromotionGoods.setPayRebateAmount(BigDecimal.ZERO);
                orderPromotionGoods.setPayBalanceAmount(promotionGoodsBo.getPayPrice());
            }
            orderPromotionGoods.setNumber(buyNum);
            orderPromotionGoods.setId(IdWorker.getId());
            orderPromotionGoodsList.add(orderPromotionGoods);
        }
        if (promotionGoodsSumPayPrice.compareTo(ruleOrderPromotionBo.getPromotionTotalPriceLimit()) > 0) {
            throw new AlertException("促销品总金额超过最大购买金额{},已购买{}", ruleOrderPromotionBo.getPromotionTotalPriceLimit(), promotionGoodsSumPayPrice);
        }
        // 允许返利支付  返利金额是否充足,足以支付促销品返利支付金额部分,如果不足则使用余额支付
        if (ruleOrderPromotionBo.getIsRebate()) {
            //  促销品返利支付金额
            BigDecimal promotionPayRebateAmount = BigDecimalUtil.getPercentPrice(promotionGoodsSumPayPrice, rebatePayProportion);
            // 合伙人可为促销品支付的返利金额 = 合伙人返利余额 - 正品返利支付金额
            BigDecimal partnerPromotionRebateAmount = partner.getRebateAmount().subtract(order.getPayRebateAmount());
            // 如果返利金额足够
            if (partnerPromotionRebateAmount.compareTo(promotionPayRebateAmount) >= 0) {
                return orderPromotionGoodsList;
            }
            // 返利金额不够
            // 获取返利支付百分比
            BigDecimal proportion = BigDecimalUtil.divide(BigDecimalUtil.multiply(partnerPromotionRebateAmount, new BigDecimal(100)), promotionPayRebateAmount);
            // 重新计算促销品支付金额
            for (OrderPromotionGoods orderPromotionGoods : orderPromotionGoodsList) {
                // 促销品返利支付金额
                // todo 这里应该使用 BigDecimalUtil.getPercentPrice(orderPromotionGoods.getPayRebateAmount(), proportion);
                BigDecimal payRebateAmount = BigDecimalUtil.getPercentPrice(orderPromotionGoods.getPayAmount(), proportion);
                // 促销品余额支付金额
                BigDecimal payBalanceAmount = orderPromotionGoods.getPayAmount().subtract(payRebateAmount);
                orderPromotionGoods.setPayRebateAmount(payRebateAmount);
                orderPromotionGoods.setPayBalanceAmount(payBalanceAmount);
            }
        }
        return orderPromotionGoodsList;
    }


    /**
     * 根据合伙人ID和商品规格ID列表获取订单商品列表
     * 应付价格为 用户折扣价格/价格体系 需要标记
     *
     * @param order                订单
     * @param goodsSpecMap         商品规格ID和数量
     * @param partnerLevelDiscount 合伙人等级折扣
     * @param specIdPriceSystems   商品规格ID和价格体系
     * @return 订单商品列表
     */
    private List<OrderGoods> getOrderGoods(Order order, Map<Long, Integer> goodsSpecMap, BigDecimal partnerLevelDiscount, Map<Long, RulePriceSystemDetailBo> specIdPriceSystems) {
        Set<Long> specIds = goodsSpecMap.keySet();
        // 1. 校验数据合法性
        // 根据传入规格查询规格和商品信息
        List<GoodsSpecBo> goodsSpecs = goodsService.getListBySpecIds(specIds);
        // 判断是否有商品规格不存在或者下架
        goodsSpecs.forEach(goodsSpec -> goodsService.checkSpecExit(goodsSpec.getGoods(), goodsSpec.getGoodsSpec(), goodsSpecMap.get(goodsSpec.getGoodsSpec().getId())));
        // 判断是否有多种不同的特殊类型
        Set<Boolean> goodsSpecialSet = goodsSpecs.stream().map(goodsSpecBo -> goodsSpecBo.getGoods().getIsSpecial()).collect(Collectors.toSet());
        if (goodsSpecialSet.size() > 1) {
            throw new AlertException("商品类型不一致,请重新下单");
        }
        // 订单商品列表
        ArrayList<OrderGoods> orderGoodsList = new ArrayList<>();

        //2. 遍历商品，计算每个商品的价格
        for (GoodsSpecBo spec : goodsSpecs) {
            // 3. 基础数据赋值
            // 规格数量
            Integer number = goodsSpecMap.get(spec.getGoodsSpec().getId());
            // 图片 如果规格有图片则使用规格图片,否则使用商品图片,不管使用哪个都是一张图片
            String picture = CharSequenceUtil.firstNonBlank(spec.getGoodsSpec().getPictures(), spec.getGoods().getPictures(), "");
            OrderGoods orderGoods = new OrderGoods();
            // 基础数据
            orderGoods.setOrderId(order.getId());
            orderGoods.setId(IdWorker.getId());
            orderGoods.setGoodsId(spec.getGoods().getId());
            orderGoods.setGoodsSpecId(spec.getGoodsSpec().getId());
            orderGoods.setGoodsTitle(spec.getGoods().getTitle());
            orderGoods.setGoodsSpecAttr(spec.getGoodsSpec().getAttr());
            orderGoods.setGoodsSpecJkyAttr(spec.getGoodsSpec().getJkyAttr());
            orderGoods.setGoodsSpecNo(spec.getGoodsSpec().getNo());
            orderGoods.setGoodsSpecBarcode(spec.getGoodsSpec().getBarcode());

            orderGoods.setGoodsSpecPicture(CollUtil.getFirst(CharSequenceUtil.split(picture, ",")));
            // 价格
            orderGoods.setOriginalAmount(spec.getGoodsSpec().getPrice());
            // 数量
            orderGoods.setNumber(number);
            // 箱规
            orderGoods.setBoxSpec(spec.getGoodsSpec().getBoxSpec());
            // 几箱几瓶的计算
            orderGoods.setBoxSpecNum(calculateBoxAndBottle(number,spec.getGoodsSpec().getBoxSpec()));
            // 商品规格属于哪个组合
            orderGoods.setGroupId(spec.getGoodsSpec().getGroupId());
            // 商品的类型是普通商品，还是组合商品
            orderGoods.setType(spec.getGoodsSpec().getType());


            // 4. 根据合伙人等级进行折扣 —— 默认为用户折扣，payPrice商品规格实付金额
            BigDecimal payPrice = BigDecimalUtil.getPayPrice(spec.getGoodsSpec().getPrice(), partnerLevelDiscount);
            orderGoods.setDiscountType(OrderGoods.DiscountTypeEnum.MEMBER_DISCOUNT.getCode());

            // 5. 根据价格体系进行计算价格
            RulePriceSystemDetailBo selectPriceSystem = specIdPriceSystems.get(spec.getGoodsSpec().getId());
            if (selectPriceSystem != null) {
                orderGoods.setRulePriceSystemId(selectPriceSystem.getRulePriceSystem().getId());
                orderGoods.setDiscountType(OrderGoods.DiscountTypeEnum.PRICE_SYSTEM.getCode());
                // 价格体系优惠类型
                RulePriceSystem.Type type = RulePriceSystem.Type.valueOf(selectPriceSystem.getRulePriceSystem().getType());
                // 优惠类型为折扣
                if (type == RulePriceSystem.Type.DISCOUNT) {
                    // 价格体系折扣价格
                    payPrice = BigDecimalUtil.getPayPrice(spec.getGoodsSpec().getPrice(), selectPriceSystem.getRulePriceSystemDetail().getDiscount());
                }
                // 优惠类型为价格 直接使用
                if (type == RulePriceSystem.Type.PRICE) {
                    payPrice = selectPriceSystem.getRulePriceSystemDetail().getPrice();
                }
            }
            orderGoods.setPayAmount(payPrice);
            orderGoods.setDiscountAmount(spec.getGoodsSpec().getPrice().subtract(payPrice));
            orderGoodsList.add(orderGoods);
        }
        // 6. 返回商品价格列表
        return orderGoodsList;
    }


    /**
     * 预计商品金额计算  实际支付返利余额可能不足
     *
     * @param orderGoodsList     订单商品列表
     * @param partner            合伙人
     * @param specIdPriceSystems 商品规格ID和价格体系
     */
    private void setEstimateGoodsAmount(Order order, List<OrderGoods> orderGoodsList, Partner partner, Map<Long, RulePriceSystemDetailBo> specIdPriceSystems) {
        // 返利支付比例，合伙人表拿的
        BigDecimal rebatePayProportion = partnerService.getRebatePayProportion(partner);
        // 商品原价
        BigDecimal goodsOriginalAmount = BigDecimal.ZERO;
        // 商品折扣金额
        BigDecimal goodsDiscountAmount = BigDecimal.ZERO;
        // 商品支付金额/应付/折扣后
        BigDecimal goodsPayAmount = BigDecimal.ZERO;
        // 商品返利支付金额
        BigDecimal goodsPayRebateAmount = BigDecimal.ZERO;
        // 商品余额支付金额
        BigDecimal goodsPayBalanceAmount = BigDecimal.ZERO;

        for (OrderGoods orderGoods : orderGoodsList) {
            //1. 计算返利支付金额
            // 价格体系
            RulePriceSystemDetailBo selectPriceSystem = specIdPriceSystems.get(orderGoods.getGoodsSpecId());
            // 价格体系不允许返利支付
            if (selectPriceSystem != null && !selectPriceSystem.getRulePriceSystem().getIsRebate()) {
                orderGoods.setPayRebateAmount(BigDecimal.ZERO);
            } else {
                orderGoods.setPayRebateAmount(BigDecimalUtil.getPercentPrice(orderGoods.getPayAmount(), rebatePayProportion));
            }
            //2. 计算余额支付金额
            orderGoods.setPayBalanceAmount(orderGoods.getPayAmount().subtract(orderGoods.getPayRebateAmount()));
            // 数量
            BigDecimal number = new BigDecimal(orderGoods.getNumber());

            //3. 设置订单表商品金额
            goodsOriginalAmount = goodsOriginalAmount.add(BigDecimalUtil.multiply(orderGoods.getOriginalAmount(), number));
            goodsDiscountAmount = goodsDiscountAmount.add(BigDecimalUtil.multiply(orderGoods.getDiscountAmount(), number));
            goodsPayAmount = goodsPayAmount.add(BigDecimalUtil.multiply(orderGoods.getPayAmount(), number));
            goodsPayRebateAmount = goodsPayRebateAmount.add(BigDecimalUtil.multiply(orderGoods.getPayRebateAmount(), number));
            goodsPayBalanceAmount = goodsPayBalanceAmount.add(BigDecimalUtil.multiply(orderGoods.getPayBalanceAmount(), number));
        }
        // 订单原价
        order.setGoodsOriginalAmount(goodsOriginalAmount);
        // 订单折扣价
        order.setGoodsDiscountAmount(goodsDiscountAmount);
        // 订单应付价
        order.setGoodsPayAmount(goodsPayAmount);
        // 订单返利支付
        order.setGoodsPayRebateAmount(goodsPayRebateAmount);
        // 订单余额支付
        order.setGoodsPayBalanceAmount(goodsPayBalanceAmount);


        // 设置订单价格
        order.setOriginalAmount(order.getGoodsOriginalAmount());
        order.setDiscountAmount(order.getGoodsDiscountAmount());
        order.setPayAmount(order.getGoodsPayAmount());
        order.setPayRebateAmount(order.getGoodsPayRebateAmount());
        order.setPayBalanceAmount(order.getGoodsPayBalanceAmount());

    }

    /**
     * 实际商品金额计算
     *
     * @param order          订单
     * @param orderGoodsList 订单商品
     * @param partner        合伙人
     */
    private void setRealGoodsAmount(Order order, List<OrderGoods> orderGoodsList, Partner partner) {
        BigDecimal partnerRebateAmount = partner.getRebateAmount();
        // 当合伙人返利余额大于订单返利支付金额时,不需要处理
        if (partnerRebateAmount.compareTo(order.getGoodsPayRebateAmount()) > 0) {
            return;
        }

        // 当没有返利支付金额时,不需要处理
        if (order.getGoodsPayRebateAmount().compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        // 计算返利余额和实际商品返利支付总额比例 被除数扩大100倍 结果取值 [0,100] 保留两位小数
        BigDecimal proportion = BigDecimalUtil.divide(BigDecimalUtil.multiply(partnerRebateAmount, new BigDecimal(100)),
                order.getGoodsPayRebateAmount());

        // 实际返利支付金额
        BigDecimal goodsPayRebateAmount = BigDecimal.ZERO;
        // 按照比例计算每个订单下商品实际返利支付金额
        for (OrderGoods orderGoods : orderGoodsList) {
            BigDecimal realityPayRebateAmount = BigDecimalUtil.getPercentPrice(orderGoods.getPayRebateAmount(), proportion);
            // 设置实际返利支付金额
            orderGoods.setPayRebateAmount(realityPayRebateAmount);
            // 设置实际余额支付金额
            orderGoods.setPayBalanceAmount(orderGoods.getPayAmount().subtract(realityPayRebateAmount));
            // 累计订单商品实际返利支付金额
            goodsPayRebateAmount = goodsPayRebateAmount.add(BigDecimalUtil.multiply(realityPayRebateAmount, BigDecimal.valueOf(orderGoods.getNumber())));
        }
        // 设置商品实际返利支付金额
        order.setGoodsPayRebateAmount(goodsPayRebateAmount);
        // 设置商品实际余额支付金额
        order.setGoodsPayBalanceAmount(order.getGoodsPayAmount().subtract(goodsPayRebateAmount));

        // 设置订单价格
        order.setOriginalAmount(order.getGoodsOriginalAmount());
        order.setDiscountAmount(order.getGoodsDiscountAmount());
        order.setPayAmount(order.getGoodsPayAmount());
        order.setPayRebateAmount(order.getGoodsPayRebateAmount());
        order.setPayBalanceAmount(order.getGoodsPayBalanceAmount());
    }


    /**
     * 获取商品满赠
     *
     * @param order          订单
     * @param orderGoodsList 订单商品
     * @param partner        合伙人
     * @return 满赠商品 Map<订单下商品 orderGoodsId,满赠商品>
     */
    private List<OrderGiveGoods> getOrderGiveGoodsByGoods(Order order, List<OrderGoods> orderGoodsList, Partner partner) {
        List<OrderGiveGoods> orderGiveGoods = new ArrayList<>();
        if (CollUtil.isEmpty(orderGoodsList)) {
            return orderGiveGoods;
        }

        BigDecimal rebateAmountBefore = orderGoodsList.stream().map(x -> x.getPayRebateAmount().multiply(BigDecimal.valueOf(x.getNumber()))).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 所有下单的specIds
        Set<Long> specIds = orderGoodsList.stream().map(OrderGoods::getGoodsSpecId).collect(Collectors.toSet());
        // 根据specIds和合伙人ID 查询满赠商品
        Map<Long, List<RuleGoodsGiveBo>> unFilterRuleGoodsGiveBoMap = ruleGoodsGiveService.getListBySpecIdsAndPartnerId(specIds, partner.getId());
        log.info("商品满赠-specId分组:{}", JacksonUtil.obj2strDelBlank(unFilterRuleGoodsGiveBoMap));
        for (OrderGoods orderGoods : orderGoodsList) {
            // 购买数量
            Integer number = orderGoods.getNumber();

            // 开始筛选符合条件的满赠商品
            List<RuleGoodsGiveBo> ruleGoodsGiveBoList = unFilterRuleGoodsGiveBoMap.get(orderGoods.getGoodsSpecId());
            if (CollUtil.isEmpty(ruleGoodsGiveBoList)) {
                continue;
            }
            for (RuleGoodsGiveBo ruleGoodsGiveBo : ruleGoodsGiveBoList) {
                Integer isRebatePay = ruleGoodsGiveBo.getIsRebatePay();
                // 根据规则是否返利，来修正每个商品的实付，和返利支付
                changeOrderGoodsListAmount(isRebatePay,Arrays.asList(orderGoods));
                // 余额支付金额
                BigDecimal payBalanceAmount = BigDecimalUtil.multiply(orderGoods.getPayBalanceAmount(), BigDecimal.valueOf(number));
                // 应付金额
                BigDecimal payAmount = BigDecimalUtil.multiply(orderGoods.getPayAmount(), BigDecimal.valueOf(number));


                // 触发条件 1.价格、2.数量
                RuleTriggerConditionEnum conditionEnum = RuleTriggerConditionEnum.getEnum(ruleGoodsGiveBo.getTriggerCondition());
                // 规则触发频率 1.每满、2.满 ; 每满为多次叠加，满为仅一次
                RuleTriggerFrequencyEnum frequencyEnum = RuleTriggerFrequencyEnum.getEnum(ruleGoodsGiveBo.getTriggerFrequency());

                // 次数/份数
                int frequency = 0;
                // 数量
                if (RuleTriggerConditionEnum.NUMBER.equals(conditionEnum)) {
                    //  满 仅一次 [最小数量,最大数量)   每满 多次叠加 仅最小数量
                    frequency = frequencyEnum.equals(RuleTriggerFrequencyEnum.FULL)
                            ? (number >= ruleGoodsGiveBo.getMinNumber() && number < ruleGoodsGiveBo.getMaxNumber() ? 1 : 0)
                            // 每满 数量 必须要满足门槛数量
                            : (number >= ruleGoodsGiveBo.getThresholdNumber() ? (number / ruleGoodsGiveBo.getMinNumber()) : 0);
                }
                // 金额
                if (RuleTriggerConditionEnum.PRICE.equals(conditionEnum)) {
                    // 触发金额类型: 1.实付金额(余额支付部分) 2.应付金额
                    RuleTriggerAmountTypeEnum amountTypeEnum = RuleTriggerAmountTypeEnum.getEnum(ruleGoodsGiveBo.getTriggerAmountType());
                    // 参与比较的金额
                    BigDecimal compareAmount = RuleTriggerAmountTypeEnum.PAY_AMOUNT.equals(amountTypeEnum) ? payAmount : payBalanceAmount;
                    // 满 仅一次 [最小金额,最大金额);  每满 多次叠加 仅最小金额
                    frequency = frequencyEnum.equals(RuleTriggerFrequencyEnum.FULL)
                            ? (compareAmount.compareTo(ruleGoodsGiveBo.getMinAmount()) >= 0 && compareAmount.compareTo(ruleGoodsGiveBo.getMaxAmount()) < 0 ? 1 : 0)
                            : (compareAmount.compareTo(ruleGoodsGiveBo.getThresholdAmount()) >= 0 ? compareAmount.divideToIntegralValue(ruleGoodsGiveBo.getMinAmount()).intValue() : 0);
                }
                if (frequency < 1) {
                    continue;
                }
                // 走到这里，证明匹配到了满赠商品规则
                // 获取赠品列表
                List<GiveConfig> list = giveConfigService.lambdaQuery().eq(GiveConfig::getRelationId, ruleGoodsGiveBo.getId())
                        .eq(GiveConfig::getGoodsSpecId, orderGoods.getGoodsSpecId())
                        .eq(GiveConfig::getType, 3)
                        .eq(GiveConfig::getIsFix, 1).list();
                for (GiveConfig giveConfig : list) {
                    OrderGiveGoods item = new OrderGiveGoods();
                    item.setOrderId(order.getId());
                    item.setOrderGoodsId(orderGoods.getId());
                    item.setGiveGoodsId(giveConfig.getGiveGoodsId());
                    GiveGoods giveGoods = giveGoodsService.getById(giveConfig.getGiveGoodsId());
                    item.setGiveGoodsTitle(giveGoods.getTitle());
                    item.setGiveGoodsBarcode(giveGoods.getBarcode());
                    item.setGiveGoodsNo(giveGoods.getNo());
                    item.setNumber(giveConfig.getFixNumber() * frequency);
                    item.setRuleGoodsGiveId(giveConfig.getRelationId());
                    item.setType(0);
                    item.setId(IdWorker.getId());
                    item.setPrice(giveConfig.getPrice());
                    orderGiveGoods.add(item);
                }
                break;
            }
        }
        // 纠正订单的实付，和返利金额
        BigDecimal rebateAmountAfter = orderGoodsList.stream().map(x -> x.getPayRebateAmount().multiply(BigDecimal.valueOf(x.getNumber()))).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal diff = rebateAmountBefore.subtract(rebateAmountAfter);
        order.setGoodsPayBalanceAmount(order.getGoodsPayBalanceAmount().add(diff));
        order.setGoodsPayRebateAmount(order.getGoodsPayRebateAmount().subtract(diff));
        order.setPayBalanceAmount(order.getPayBalanceAmount().add(diff));
        order.setPayRebateAmount(order.getPayRebateAmount().subtract(diff));
        return orderGiveGoods;
    }


    /**
     * 获取订单满赠
     *
     * @param order          订单
     * @param orderGoodsList 订单商品
     * @param partner        合伙人
     * @return 满赠商品 Map<订单下商品 orderGoodsId,满赠商品>
     */
    private void getOrderGiveGoodsByOrder(OrderConfirmResp resp, Order order, List<OrderGoods> orderGoodsList, Partner partner) {
        List<OrderGiveGoods> orderGiveGoods = new ArrayList<>();
        resp.setOrderGives(orderGiveGoods);
        // 所有下单的specIds
        Set<Long> specIds = orderGoodsList.stream().map(OrderGoods::getGoodsSpecId).collect(Collectors.toSet());
        // 根据specIds和合伙人ID 查询满赠商品
        for (RuleOrderGiveBo ruleOrderGiveBo : ruleOrderGiveService.getListBySpecIdsAndPartnerId(specIds, partner.getId())) {
            log.info("订单满赠:{}", JacksonUtil.obj2strDelBlank(ruleOrderGiveBo));
            // 筛选参与的规格
            List<OrderGoods> filterOrderGoods = ruleOrderGiveBo.getIsAllSpec()
                    ? orderGoodsList
                    : orderGoodsList.stream().filter(orderGoods -> ruleOrderGiveBo.getSpecIds().contains(orderGoods.getGoodsSpecId())).collect(Collectors.toList());
            log.info("参与的规格:{}", JacksonUtil.obj2strDelBlank(filterOrderGoods));
            // 规则触发频率 1.每满、2.满 ; 每满为多次叠加，满为仅一次
            RuleTriggerFrequencyEnum frequencyEnum = RuleTriggerFrequencyEnum.getEnum(ruleOrderGiveBo.getTriggerFrequency());
            // 触发金额类型: 1.实付金额(余额支付部分) 2.应付金额
            RuleTriggerAmountTypeEnum amountTypeEnum = RuleTriggerAmountTypeEnum.getEnum(ruleOrderGiveBo.getTriggerAmountType());
            // 参与比较的金额
            BigDecimal compareAmount = RuleTriggerAmountTypeEnum.PAY_BALANCE_AMOUNT.equals(amountTypeEnum)
                    ? filterOrderGoods.stream().map(orderGoods -> orderGoods.getPayBalanceAmount().multiply(BigDecimal.valueOf(orderGoods.getNumber()))).reduce(BigDecimal.ZERO, BigDecimal::add)
                    : filterOrderGoods.stream().map(orderGoods -> orderGoods.getPayAmount().multiply(BigDecimal.valueOf(orderGoods.getNumber()))).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 次数/份数  满 仅一次 [最小金额,最大金额)  每满 多次叠加 仅最小金额
            int frequency = 0;
            // 如果满1次
            if (RuleTriggerFrequencyEnum.FULL.equals(frequencyEnum)) {
                frequency = compareAmount.compareTo(ruleOrderGiveBo.getMinAmount()) >= 0 && compareAmount.compareTo(ruleOrderGiveBo.getMaxAmount()) < 0 ? 1 : 0;
            } else{
                // 判断每满是否达到门槛金额，没达到门槛，换下一条
                if (compareAmount.compareTo(ruleOrderGiveBo.getThresholdAmount()) < 0) {
                    continue;
                }
                // 每满
                frequency = compareAmount.divideToIntegralValue(ruleOrderGiveBo.getMinAmount()).intValue();
            }
            if (frequency < 1) {
                continue;
            }
            log.info("匹配 {}份 订单满赠规则:{}", frequency, JacksonUtil.obj2strDelBlank(ruleOrderGiveBo));
            // 按照固定赠品
            if (ruleOrderGiveBo.getSelectGive() == 0) {
                for (RuleRelationGiveGoodsBo relationGiveGoodsBo : ruleOrderGiveBo.getGiveGoodsList()) {
                    OrderGiveGoods item = new OrderGiveGoods();
                    item.setOrderId(order.getId());
                    item.setGiveGoodsId(relationGiveGoodsBo.getGiveGoods().getId());
                    item.setGiveGoodsTitle(relationGiveGoodsBo.getGiveGoods().getTitle());
                    item.setGiveGoodsBarcode(relationGiveGoodsBo.getGiveGoods().getBarcode());
                    item.setGiveGoodsNo(relationGiveGoodsBo.getGiveGoods().getNo());
                    item.setNumber(relationGiveGoodsBo.getNumber() * frequency);
                    item.setRuleOrderGiveId(ruleOrderGiveBo.getId());
                    item.setId(IdWorker.getId());
                    // 1 表示订单满赠
                    item.setType(1);
                    // 订单下固定赠品，这里取的还是老表：rule_relation_give_goods。 没有从give_config拿
                    item.setPrice(relationGiveGoodsBo.getPrice());
                    orderGiveGoods.add(item);

                }
                resp.setOrderGives(orderGiveGoods);
                break;
            }
            resp.setRuleOrderGiveBo(ruleOrderGiveBo);
            // 按照任选赠品
            if (ruleOrderGiveBo.getSelectGive() == 1) {
                // 设置最大赠送数量
                ruleOrderGiveBo.setMaxGiveNumber(ruleOrderGiveBo.getMaxGiveNumber() * frequency);


                // 计算任选赠品的列表
                List<GiveConfig> freeList = giveConfigService.lambdaQuery().eq(GiveConfig::getRelationId, ruleOrderGiveBo.getId())
                        .eq(GiveConfig::getType, 2).list();
                ArrayList<GiveConfigBo> giveConfigBos = new ArrayList<>();
                ruleOrderGiveBo.setGiveGoodsFreeList(giveConfigBos);
                for (GiveConfig giveConfig : freeList) {
                    GiveConfigBo giveConfigBo = new GiveConfigBo();
                    BeanUtil.copyProperties(giveConfig,giveConfigBo);
                    // 条形码
                    giveConfigBo.setBarcode(giveGoodsService.getById(giveConfig.getGiveGoodsId()).getBarcode());
                    // 设置每一行最大数量
                    if (giveConfigBo.getMaxNumber() != null) {
                        giveConfigBo.setMaxNumber(giveConfigBo.getMaxNumber() * frequency);
                    }
                    if (giveConfigBo.getMaxAmount() != null) {
                        // 设置每一行最大金额
                        giveConfigBo.setMaxAmount(giveConfigBo.getMaxAmount().multiply(BigDecimal.valueOf(frequency)));
                    }
                    giveConfigBos.add(giveConfigBo);
                }
                log.info("订单满赠id:{},任选赠品列表{}",ruleOrderGiveBo.getId(),giveConfigBos);



                // 最大金额类型
                Integer type = ruleOrderGiveBo.getType();
                // 固定金额
                if (type == 0) {
                    BigDecimal allMaxAmount = ruleOrderGiveBo.getMaxGiveAmount().multiply(BigDecimal.valueOf(frequency));
                    ruleOrderGiveBo.setMaxGiveAmount(allMaxAmount);
                    return;
                }
                Boolean isAllSpec = ruleOrderGiveBo.getIsAllSpec();
                List<OrderGoods> collect = new ArrayList<>();
                if (isAllSpec) {
                    // 全部参与赠品最大金额计算
                    collect = filterOrderGoods;
                }
                if (!isAllSpec) {
                    // 查询允许计算赠品金额的商品
                    List<RuleRelationGoodsSpec> ruleRelationGoodsSpecList = ruleRelationGoodsSpecService.lambdaQuery().eq(RuleRelationGoodsSpec::getIsJoin, 1)
                            .eq(RuleRelationGoodsSpec::getRelationId, ruleOrderGiveBo.getId()).list();
                    List<Long> ruleRelationGoodsSpecIds = ruleRelationGoodsSpecList.stream().map(x -> x.getGoodsSpecId()).collect(Collectors.toList());
                    // 参与计算赠品的最大金额的商品
                    collect = filterOrderGoods.stream().filter(x -> ruleRelationGoodsSpecIds.contains(x.getGoodsSpecId())).collect(Collectors.toList());
                    log.info("参与计算赠品的最大金额的商品{}",filterOrderGoods);
                }
                // 金额比例
                BigDecimal proportion = ruleOrderGiveBo.getMaxGiveAmount();
                // 按应付金额比例，包括返利
                if (type == 1) {
                    BigDecimal maxAmount = collect.stream().map(orderGoods -> orderGoods.getPayAmount().multiply(BigDecimal.valueOf(orderGoods.getNumber()))).reduce(BigDecimal.ZERO, BigDecimal::add);
                    log.info("任选赠品计算按照应付金额比例类型，参与计算金额{},金额比例{}",maxAmount,proportion);
                    ruleOrderGiveBo.setMaxGiveAmount(BigDecimalUtil.getPercentPrice(maxAmount,proportion));
                    return;
                }
                // 按实付金额比例
                if (type == 2) {
                    BigDecimal maxAmount = collect.stream().map(orderGoods -> orderGoods.getPayBalanceAmount().multiply(BigDecimal.valueOf(orderGoods.getNumber()))).reduce(BigDecimal.ZERO, BigDecimal::add);
                    log.info("任选赠品计算按照实付金额比例类型，参与计算金额{},金额比例{}",maxAmount,proportion);
                    ruleOrderGiveBo.setMaxGiveAmount(BigDecimalUtil.getPercentPrice(maxAmount,proportion));
                    return;
                }

            }
            break;

        }
    }


    /**
     * 获取订单返利
     *
     * @param order          订单
     * @param orderGoodsList 订单商品
     * @param partner        合伙人
     * @return OrderRebate 返利
     */
    private OrderRebate getOrderRebate(Order order, List<OrderGoods> orderGoodsList, Partner partner) {
        OrderRebate orderRebate = null;
        Set<Long> orderAllSpecIds = orderGoodsList.stream().map(OrderGoods::getGoodsSpecId).collect(Collectors.toSet());
        // 数据库查询相关的返利规则
        for (RuleOrderRebateBo ruleOrderRebateBo : ruleOrderRebateService.getListBySpecIdsAndPartnerId(orderAllSpecIds, partner.getId())) {
            // 规格关系
            List<RuleRelationGoodsSpec> specs = ruleOrderRebateBo.getSpecs();
            // 所有的参与计算的规格ID
            Set<Long> specIds = specs.stream().map(RuleRelationGoodsSpec::getGoodsSpecId).collect(Collectors.toSet());
            // 筛选参与的规格
            List<OrderGoods> filterOrderGoods = ruleOrderRebateBo.getIsAllSpec()
                    ? orderGoodsList
                    : orderGoodsList.stream().filter(orderGoods -> specIds.contains(orderGoods.getGoodsSpecId())).collect(Collectors.toList());
            // 触发金额类型: 1.实付金额(余额支付部分) 2.应付金额
            RuleTriggerAmountTypeEnum amountTypeEnum = RuleTriggerAmountTypeEnum.getEnum(ruleOrderRebateBo.getTriggerAmountType());
            // 返利类型  1.固定金额 2.金额比例
            RuleRebateTypeEnum rebateTypeEnum = RuleRebateTypeEnum.getEnum(ruleOrderRebateBo.getRebateType());
            // 参与比较的金额
            BigDecimal compareAmount = RuleTriggerAmountTypeEnum.PAY_BALANCE_AMOUNT.equals(amountTypeEnum)
                    ? filterOrderGoods.stream().map(orderGoods -> orderGoods.getPayBalanceAmount().multiply(BigDecimal.valueOf(orderGoods.getNumber()))).reduce(BigDecimal.ZERO, BigDecimal::add)
                    : filterOrderGoods.stream().map(orderGoods -> orderGoods.getPayAmount().multiply(BigDecimal.valueOf(orderGoods.getNumber()))).reduce(BigDecimal.ZERO, BigDecimal::add);

            // 比较金额范围 [minAmount,maxAmount)
            if (compareAmount.compareTo(ruleOrderRebateBo.getMinAmount()) >= 0 && compareAmount.compareTo(ruleOrderRebateBo.getMaxAmount()) < 0) {
                // 返利金额
                BigDecimal rebateAmount = BigDecimal.ZERO;
                // 固定金额
                if (RuleRebateTypeEnum.FIXED_AMOUNT.equals(rebateTypeEnum)) {
                    rebateAmount = ruleOrderRebateBo.getFixedAmount();
                }
                // 比例返利
                if (RuleRebateTypeEnum.AMOUNT_RATIO.equals(rebateTypeEnum)) {
                    // 排除掉不参与返利金额发放的商品
                    Set<Long> rebateSpecIds = specs.stream().filter(RuleRelationGoodsSpec::getIsRebate).map(RuleRelationGoodsSpec::getGoodsSpecId).collect(Collectors.toSet());
                    BigDecimal grantAmount = filterOrderGoods.stream()
                            // 筛选仅参与返利的商品
                            .filter(orderGoods -> ruleOrderRebateBo.getIsAllSpec() || rebateSpecIds.contains(orderGoods.getGoodsSpecId()))
                            .map(orderGoods -> orderGoods.getPayBalanceAmount().multiply(BigDecimal.valueOf(orderGoods.getNumber())))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    rebateAmount = BigDecimalUtil.getPercentPrice(grantAmount, ruleOrderRebateBo.getProportion());
                }

                orderRebate = new OrderRebate();
                orderRebate.setTitle(ruleOrderRebateBo.getTitle());
                orderRebate.setOrderId(order.getId());
                orderRebate.setPartnerId(partner.getId());
                orderRebate.setAmount(rebateAmount);
                orderRebate.setIsArrival(false);
                orderRebate.setRuleOrderRebateId(ruleOrderRebateBo.getId());
                orderRebate.setId(IdWorker.getId());
                log.info("匹配到的返利{},返利金额:{}", ruleOrderRebateBo, rebateAmount);
                break;
            }
        }
        return orderRebate;
    }


    /**
     * 订单详情
     *
     * @param id 订单id
     * @return OrderAllInfoResp
     */
    public OrderAllInfoResp getOrderAllInfoById(Long id) {
        OrderAllInfoResp order = baseMapper.getOrderAllInfoById(id);
        // 订单下所有类型赠品
        List<OrderGiveGoods> gives = order.getOrderGives();

        // 过滤出商品满赠
        List<OrderGiveGoods> goodsGives = gives.stream().filter(orderGiveGoods -> orderGiveGoods.getType() == 0).collect(Collectors.toList());
        // 过滤出订单满赠
        List<OrderGiveGoods> orderGives = gives.stream().filter(orderGiveGoods -> orderGiveGoods.getType() == 1).collect(Collectors.toList());
        // 每个组合下的赠品
        Map<Long, List<OrderGiveGoods>> groupGivesMap = gives.stream().filter(orderGiveGoods -> orderGiveGoods.getType() == 2).collect(Collectors.groupingBy(x -> x.getGroupId()));


        // 订单满赠类型，赠品的价格字段
        if (CollUtil.isNotEmpty(orderGives)) {
            ArrayList<OrderGiveGoodsBo> orderGiveGoodsBos = new ArrayList<>();
            // 订单满赠是固定，还是任选
            Integer selectGive = ruleOrderGiveService.getById(orderGives.get(0).getRuleOrderGiveId()).getSelectGive();
            // 处理订单下赠品的价格字段。
            for (OrderGiveGoods orderGive : orderGives) {
                OrderGiveGoodsBo orderGiveGoodsBo = new OrderGiveGoodsBo();
                BeanUtil.copyProperties(orderGive,orderGiveGoodsBo);
//                // 订单满赠类型是固定
//                if (selectGive == 0) {
//                    RuleRelationGiveGoods one = relationGiveGoodService.lambdaQuery().eq(RuleRelationGiveGoods::getRelationId, orderGive.getRuleOrderGiveId())
//                            .eq(RuleRelationGiveGoods::getGiveGoodsId, orderGive.getGiveGoodsId())
//                            .one();
//                    orderGiveGoodsBo.setPrice(one.getPrice());
//                }
//                // 任选
//                if (selectGive == 1) {
//                    GiveConfig giveConfig = giveConfigService.lambdaQuery().select(GiveConfig::getPrice).eq(GiveConfig::getRelationId, orderGive.getRuleOrderGiveId())
//                            .eq(GiveConfig::getGiveGoodsId, orderGive.getGiveGoodsId())
//                            .one();
//                    orderGiveGoodsBo.setPrice(giveConfig.getPrice());
//                }

                orderGiveGoodsBos.add(orderGiveGoodsBo);
                order.setOrderGiveGoodsBos(orderGiveGoodsBos);
            }

        }
        order.setOrderGives(orderGives);
        order.setGoodsGives(goodsGives);


        // 下单商品
        List<OrderGoods> orderGoods = order.getOrderGoods();


        // 属于组合下商品，按照组合ID分组
        Map<Long, List<OrderGoods>> groupGoodsMap = orderGoods.stream().filter(x -> x.getType() == 1).collect(Collectors.groupingBy(x -> x.getGroupId()));
        // 普通商品
        List<OrderGoods> orderGoodsExclueGroup= orderGoods.stream().filter(x -> x.getType() == 0).collect(Collectors.toList());
        order.setOrderGoods(orderGoodsExclueGroup);


        // 汇总所有的组合ID，查询组合名
        HashSet<Long> groupIds = new HashSet<>();
        groupIds.addAll(groupGivesMap.keySet());
        groupIds.addAll(groupGoodsMap.keySet());
        if (CollUtil.isNotEmpty(groupIds)) {
            List<Groups> groupsList = groupService.lambdaQuery().in(Groups::getId, groupIds).list();
            // ID name
            Map<Long, String> groupMap = groupsList.stream().collect(Collectors.toMap(x -> x.getId(), y -> y.getTitle()));


            // 组合下返回商品
            HashMap<String, List<OrderGoods>> groupGoodsRes = new HashMap<>();
            for (Long groupId : groupGoodsMap.keySet()) {
                List<OrderGoods> orderGoodsList = groupGoodsMap.get(groupId);
                String title = groupMap.get(groupId);
                groupGoodsRes.put(title, orderGoodsList);
            }
            // 组合下商品，（不包含普通商品，分割开的）
            order.setGroupGoodsRes(groupGoodsRes);

            // 组合下返回赠品
            HashMap<String, List<OrderGiveGoods>> groupGiveGoodsRes = new HashMap<>();
            for (Long groupId : groupGivesMap.keySet()) {
                // 赠品
                List<OrderGiveGoods> orderGiveGoods = groupGivesMap.get(groupId);
                // 组合名字
                String name = groupMap.get(groupId);
                groupGiveGoodsRes.put(name,orderGiveGoods);
            }
            // 组合下赠品
            order.setGroupGiveGoodsRes(groupGiveGoodsRes);
        }




        // 下单人信息
        if (order.getOrder().getUserId() != null && order.getOrder().getUserId() > 0) {
            order.setUser(userService.getById(order.getOrder().getUserId()));
        }
        // 处理订单详情下的售后单
        handleAfterSaleGoods(order);
        return order;
    }

    /**
     * 处理订单详情下的售后单
     * @param order
     */
    public void handleAfterSaleGoods(OrderAllInfoResp order) {
        List<Long> deliveryIds = orderDeliveryService.lambdaQuery().eq(OrderDelivery::getOrderId, order.getOrderId()).list()
                .stream().map(OrderDelivery::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(deliveryIds)) {
            return;
        }
        List<ReimbursementRefundsDto> list1 = reimbursementRefundsMapper.listReimbursementRefundsDtosByDeliveryIds(deliveryIds);
        order.setReimbursementRefundsList(list1);
        List<ExchangeDeliveryDto> list2 = exchangeDeliveryMapper.listExchangeDeliveryDtosByDeliveryIds(deliveryIds);
        order.setExchangeDeliveryList(list2);
        List<WrongMissDeliveryDto> list3 = wrongMissDeliveryMapper.listWrongMissDeliveryDtosByDeliveryIds(deliveryIds);
        order.setWrongMissDeliveryList(list3);
        //order.setAfterSaleList(afterSaleList);
    }


    /**
     * app分页订单列表
     *
     * @param pageReq   分页参数
     * @param partnerId 合伙人id
     * @param state     订单状态
     */
    public Page<OrderResp> pageWithOrderGoodsByPartnerId(PageReq pageReq, Long partnerId, Integer state,OrderQuery orderQuery) {
        Page<OrderResp> page = new Page<>(pageReq.getPage(), pageReq.getPageSize(), false);
        // 如果要查询售后状态
        // 查询哪些订单ID列表，在售后中
        List<Long> orderIds = null;
        if (ObjectUtil.isNotNull(state) && state == 4) {
            orderIds = orderMapper.getOrderIdInAfterList(partnerId);
            orderIds.add(-1L);
        }
        page.setTotal(baseMapper.pageWithOrderCount(partnerId, state,orderQuery,orderIds));
        List<OrderResp> orders = baseMapper.pageWithOrderGoodsByPartnerId(pageReq, partnerId, state,orderQuery,orderIds);
        if (ObjectUtil.isNotNull(state) && state == 4) {
            orders.stream().forEach(x->x.setIsAfterSale(true));
        }
        // 处理订单状态，是否显示确认收货
        if (ObjectUtil.isNotNull(state) && state != 4) {
            handleOrder(orders);
        }
        page.setRecords(orders);
        return page;
    }

    /**
     * 确认收货,已完成订单
     *
     * @param order 订单
     */
    public void confirmReceipt(Order order) {
        Order.State state = Order.State.getEnum(order.getState());
        // 仅当是 待发货 待收货/已发货 状态才能确认收货
        if (!Order.State.WAIT_SHIP.equals(state) && !Order.State.WAIT_RECEIVE.equals(state)) {
            log.error("订单号:{},当前状态:{},仅待发货、待收货/已发货 状态才能确认收货", order.getId(), state.getDesc());
            throw new AlertException("订单状态不正确");
        }
        transactionTemplate.execute(status -> {
            try {
                // 确认收货
                lambdaUpdate()
                        .eq(Order::getId, order.getId())
                        .set(Order::getState, Order.State.COMPLETE.getCode())
                        .update(new Order());

                // 发放返利
                orderRebateService.lambdaQuery()
                        .eq(OrderRebate::getOrderId, order.getId())
                        .isNotNull(OrderRebate::getRuleOrderRebateId)
                        .eq(OrderRebate::getIsArrival, false)
                        .last(SqlConstants.LIMIT_ONE)
                        .oneOpt()
                        .ifPresent(orderRebate -> {
                            // 发放返利
                            orderRebateService.lambdaUpdate()
                                    .eq(OrderRebate::getId, orderRebate.getId())
                                    .set(OrderRebate::getIsArrival, true)
                                    .update(new OrderRebate());
                            // 确认收货发返利，加入返利列表，自动审核通过
                            RebateApplication rebateApplication = new RebateApplication();
                            rebateApplication.setOrderId(order.getId());
                            User user = Auth.getUserThrE();
                            rebateApplication.setPartnerId(user.getPartnerId());
                            rebateApplication.setUserId(user.getId());
                            rebateApplication.setAdminId(null);
                            rebateApplication.setAmount(orderRebate.getAmount());
                            // 自动审核通过
                            rebateApplication.setState(2);
                            rebateApplication.setNotes("订单返利发放");
                            rebateApplication.setId(IdWorker.getId());

                            rebateApplicationMapper.insert(rebateApplication);
                            // 加入收支明细账单
                            partnerService.addRebateBalance(orderRebate.getPartnerId(), orderRebate.getAmount(), "订单返利发放", OrderRebate.class, PartnerPayment.RelationType.REBATE_RECHARGE, orderRebate.getId(),order.getId(),null);
                        });
            }catch (Exception e) {
                status.setRollbackOnly();
                log.error("确认收货失败,原因：{}", e.getMessage());
                throw new AlertException("确认收货失败,原因：{}", e.getMessage());
            }
            return null;
        });


        // 同步海软云
        syncToHrYunIfOfflineChannel(order, "订单已完成");
    }


    /**
     * 根据订单ID和合伙人ID查询订单,如果不存在则报错
     *
     * @param orderId   订单ID
     * @param partnerId 合伙人ID
     */
    public Order getOrderByOrderIdAndPartnerId(Long orderId, Long partnerId) {
        return lambdaQuery()
                .eq(Order::getId, orderId)
                .eq(Order::getPartnerId, partnerId)
                .oneOpt()
                .orElseThrow(() -> new AlertException("订单不存在"));
    }


    /**
     * 同步吉客云发货单
     * 以非事务方式执行，如果当前存在事务，则抛出异常。
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NEVER)
    public void jkyCreateByOrderId(Long orderId) {

        Order order = Optional.ofNullable(getById(orderId))
                .orElseThrow(() -> new AlertException("订单不存在"));

        // 校验订单状态
        if (order.getState().equals(Order.State.WAIT_PAY.getCode())) {
            throw new AlertException("未付款订单，禁止手动同步!");
        }
        if (order.getState().equals(Order.State.CANCEL.getCode())) {
            throw new AlertException("订单已取消，禁止手动同步!");
        }

        // 查询合伙人的渠道信息
        Partner partner = partnerService.getById(order.getPartnerId());
        if (partner == null) {
            throw new AlertException("合伙人不存在");
        }
        // 渠道信息
        Channel channel = channelService.getById(partner.getChannelId());
        if (channel == null) {
            throw new AlertException("渠道不存在");
        }


        // 查询该订单下所有未同步的发货单
        List<OrderDeliveryBo> deliveries = orderDeliveryService.listByOrderIdAndNotSync(orderId);
        // 同步到吉客云
        for (OrderDeliveryBo delivery : deliveries) {
            String jkNo = jackYunService.createDelivery(delivery, channel,order);
            // 同步成功后更新发货单
            if (CharSequenceUtil.isNotBlank(jkNo)) {
                delivery.setJyNo(jkNo);
                delivery.setIsSync(true);
                // 将发货单业务员同步到吉客云，更新自定义字段
                jackYunService.updateBusiness(jkNo,delivery.getBusinessName(),delivery.getCustomNotes(),delivery);
            } else {
                // 同步失败后更新订单为同步失败
                lambdaUpdate()
                        .eq(Order::getId, orderId)
                        .set(Order::getIsSyncError, true)
                        .update(new Order());
            }
            orderDeliveryService.updateById(delivery);
        }

        // 查询未同步的发货单数量 如果为0 则更新订单为同步成功
        Long notSyncCount = orderDeliveryService
                .lambdaQuery()
                .eq(OrderDelivery::getOrderId, orderId)
                .eq(OrderDelivery::getIsSync, false)
                .count();
        if (notSyncCount == 0) {
            lambdaUpdate()
                    .eq(Order::getId, orderId)
                    .set(Order::getIsSyncError, false)
                    .update(new Order());
        } else {
            throw new AlertException("订单发货单同步失败，具体原因请查看发货单");
        }

    }

    /**
     * 根据订单（发货单）拉取吉客云订单信息
     */
    public void jkyGetOrderDeliveryByOrderId(Long orderId) {
        Order order = getById(orderId);
        Order.State state = Order.State.getEnum(order.getState());

        // 待付款直接跳出
        if (Order.State.WAIT_PAY.equals(state)) {
            return;
        }

        // 仅在待发货，待收货下拉取数据
        if (!Order.State.WAIT_SHIP.equals(state) && !Order.State.WAIT_RECEIVE.equals(state)) {
            log.error("订单号:{},当前状态:{},仅已付款、待发货状态下拉取数据", order.getId(), state.getDesc());
            throw new AlertException("订单状态不正确");
        }
        // 所有的发货单信息
        List<OrderDelivery> syncBeforeDeliveries = orderDeliveryService.lambdaQuery().eq(OrderDelivery::getOrderId, orderId).list();
        // 批量查询吉客云订单信息
        jackYunService.syncOrder(order, syncBeforeDeliveries);
        // 查询最新得发货单 根据发货单更新订单
        List<OrderDelivery> syncAfterDeliveries = orderDeliveryService.lambdaQuery().eq(OrderDelivery::getOrderId, orderId).list();

        // 如果发货单全部取消,则: 取消订单
        boolean isAllCancel = syncAfterDeliveries.stream().allMatch(delivery
                -> OrderDelivery.State.getCanceledState().contains(OrderDelivery.State.getEnum(delivery.getState())));
        if (isAllCancel) {
            log.info("订单:{} 发货单全部取消,取消订单", order.getId());
            cancelOrder(order, "吉客云取消订单");
            return;
        }

        // 如果发货单全部 已完成,则: 订单完成
        boolean isAllCompleted = syncAfterDeliveries.stream().allMatch(delivery
                -> getCompleted().contains(OrderDelivery.State.getEnum(delivery.getState())));
        if (isAllCompleted) {
            log.info("订单:{} 发货单全部 已完成 订单完成", order.getId());
            confirmReceipt(order);

            return;
        }

        // 发货单有其中一个为已发货,则: 更新订单为已发货
        boolean isAnyShipped = syncAfterDeliveries.stream().anyMatch(delivery
                -> OrderDelivery.State.getDeliveryState().contains(OrderDelivery.State.getEnum(delivery.getState())));
        if (isAnyShipped) {
            log.info("订单:{} 发货单有其中一个为已发货,更新订单为已发货", order.getId());
            lambdaUpdate()
                    .eq(Order::getId, orderId)
                    .set(Order::getState, Order.State.WAIT_RECEIVE.getCode())
                    .update(new Order());
            // 同步海软云 - 订单变为待收货状态
            syncToHrYunIfOfflineChannel(order, "订单变为待收货状态");
            return;
        }


        // 发货单全部为"待审核或取消"【待审核1010、已取消5010、 已取消被合并5020、已取消被拆分5030】 则订单重新回到 "待发货" 状态。
        boolean isAllWaitAuditOrCancel = syncAfterDeliveries.stream().allMatch(delivery
                -> OrderDelivery.State.getWaitDeliveryState().contains(OrderDelivery.State.getEnum(delivery.getState())));
        if (isAllWaitAuditOrCancel && Order.State.WAIT_RECEIVE.equals(state)) {
            log.info("订单:{} 发货单全部为待审核或取消,订单重新回到待发货状态", order.getId());
            lambdaUpdate()
                    .eq(Order::getId, orderId)
                    .set(Order::getState, Order.State.WAIT_SHIP.getCode())
                    .update(new Order());
        }

    }

    /**
     * 同步订单到海软云（仅限线下渠道）
     *
     * @param order     订单信息
     * @param operation 操作描述，用于日志记录
     */
    private void syncToHrYunIfOfflineChannel(Order order, String operation) {
        Objects.requireNonNull(order, "订单不能为空");
        Objects.requireNonNull(order.getId(), "订单ID不能为空");
        Objects.requireNonNull(order.getPartnerId(), "合伙人ID不能为空");

        try {
            // 校验订单是否满足同步条件
            String errorReason = validateOrderForHrYunSync(order);
            if (ObjectUtil.isNotNull(errorReason)) {
                log.debug("订单[{}]{}，跳过海软云同步", order.getId(), errorReason);
                return;
            }

            log.info("开始同步订单[{}]到海软云，操作：{}", order.getId(), operation);

            // 调用海软云同步服务
            hrYunService.syncOrder(order);

            log.info("订单[{}]同步海软云成功，操作：{}", order.getId(), operation);

        } catch (Exception e) {
            // 海软云同步失败不应该影响主业务流程，记录错误日志即可
            log.error("订单[{}]同步海软云失败，操作：{}，错误信息：{}", order.getId(), operation, e.getMessage(), e);
        }
    }

    /**
     * 手动同步订单到海软云
     * 提供给管理员手动触发订单同步的接口
     *
     * @param orderId 订单ID
     * @return 同步结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Response<String> manualSyncToHrYun(Long orderId) {
        Objects.requireNonNull(orderId, "订单ID不能为空");

        try {
            Order order = getById(orderId);
            if (ObjectUtil.isNull(order)) {
                return Response.error("订单不存在");
            }

            // 手动同步需要额外校验订单状态
            Order.State state = Order.State.getEnum(order.getState());
            if (!Order.State.WAIT_RECEIVE.equals(state) && !Order.State.COMPLETE.equals(state)) {
                return Response.error("订单状态不正确，只有待收货或已完成的订单才能同步到海软云");
            }

            // 校验订单是否满足同步条件
            String errorReason = validateOrderForHrYunSync(order);
            if (ObjectUtil.isNotNull(errorReason)) {
                return Response.error(errorReason);
            }

            log.info("管理员手动触发订单[{}]同步到海软云", orderId);

            // 调用海软云同步服务
            hrYunService.syncOrder(order);

            return Response.success("订单同步海软云成功");

        } catch (Exception e) {
            log.error("手动同步订单[{}]到海软云失败：{}", orderId, e.getMessage(), e);
            return Response.error("同步失败：" + e.getMessage());
        }
    }

    /**
     * 校验订单是否满足海软云同步条件
     *
     * @param order 订单信息
     * @return 错误原因，如果返回null表示校验通过
     */
    private String validateOrderForHrYunSync(Order order) {
        Objects.requireNonNull(order, "订单不能为空");
        Objects.requireNonNull(order.getId(), "订单ID不能为空");
        Objects.requireNonNull(order.getPartnerId(), "合伙人ID不能为空");

        // 查询合伙人信息
        Partner partner = partnerService.getById(order.getPartnerId());
        if (ObjectUtil.isNull(partner)) {
            return String.format("合伙人[%s]不存在", order.getPartnerId());
        }

        // 判断是否为线下渠道（channelId = 1）
        if (ObjectUtil.isNull(partner.getChannelId()) || !(partner.getChannelId().equals(1L) || partner.getChannelId().equals(1879058677636845569L))) {
            return String.format("合伙人[%s]不是线下渠道（channelId=%s），无法同步到海软云",
                    partner.getId(), partner.getChannelId());
        }

        // 检查订单是否已同步过海软云
        if (ObjectUtil.isNotNull(order.getSyncHry()) && order.getSyncHry().equals(1)) {
            return "订单已同步过海软云，无需重复同步";
        }

        return null; // 校验通过
    }

    /**
     * 取消订单
     *
     * @param order       订单
     * @param closeReason 取消原因
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder(Order order, String closeReason) {
        if (!order.getState().equals(Order.State.WAIT_PAY.getCode())
                && !order.getState().equals(Order.State.WAIT_SHIP.getCode())) {
            log.error("订单:{} 状态不是待付款或待发货，无法取消", order.getId());
            throw new AlertException("订单状态不是待付款或待发货，不能取消订单!");
        }
        // 查询全部的发货单
        List<OrderDelivery> deliveries = orderDeliveryService.lambdaQuery().eq(OrderDelivery::getOrderId, order.getId()).list();
        // 如果有发货在途或者已完成的订单,则不允许取消
        if (deliveries.stream().anyMatch(delivery ->
                OrderDelivery.State.getDeliveryState().contains(OrderDelivery.State.getEnum(delivery.getState()))
                        || delivery.getState().equals(OrderDelivery.State.COMPLETED.getCode()))) {
            throw new AlertException("订单内已有发货单已发货，不能取消订单!");
        }
        jackYunService.cancelOrder(order, deliveries, closeReason);
        // 如果已支付,则退款
        if (order.getState().equals(Order.State.WAIT_SHIP.getCode())) {
            // 返利支付>0 退返利
            if (order.getPayRebateAmount().compareTo(BigDecimal.ZERO) > 0) {

                // 退返利
                partnerService.addRebateBalance(order.getPartnerId(), order.getPayRebateAmount(), "订单取消退返利", Order.class, PartnerPayment.RelationType.REBATE_TRADE_REFUND, order.getId(),order.getId(),null);
            }
            // 余额支付>0 退余额
            if (order.getPayBalanceAmount().compareTo(BigDecimal.ZERO) > 0) {
                // 退余额
                partnerService.addBalance(order.getPartnerId(), order.getPayBalanceAmount(), "订单取消退余额", Order.class, PartnerPayment.RelationType.BALANCE_TRADE_REFUND, order.getId(),order.getId(),null,null);
            }
        }

        // 更新订单状态
        boolean update = lambdaUpdate()
                .eq(Order::getId, order.getId())
                .set(Order::getState, Order.State.CANCEL.getCode())
                .set(Order::getCancelTime, LocalDateTime.now())
                .set(Order::getCloseReason, closeReason)
                .set(Order::getIsSyncError, false)
                .update(new Order());
        if (!update) {
            throw new AlertException("取消订单失败,无法更新订单状态!");
        }
    }


    /**
     * 可开票的订单列表，附带可开票金额
     *
     * @param partnerId      合伙人ID
     * @param filterOrderIds 过滤的订单ID 空取全部
     * @return List<OrderInvoiceAmount>
     */
    public List<OrderInvoiceAmount> getCanOpenInvoiceOrderListWithInvoiceAmount(Long partnerId, Collection<Long> filterOrderIds) {
        return baseMapper.getCanOpenInvoiceOrderListWithInvoiceAmount(partnerId, filterOrderIds);
    }

    /**
     * 处理订单列表状态，是否显示确认收货按钮
     * @param orders
     */
    public void handleOrder(List<OrderResp> orders) {
        if (CollUtil.isEmpty(orders)) {
            return;
        }
        //  1.待付款 2.待发货  10.订单完成 11.订单取消，
        List<Integer> list = Arrays.asList(1, 2, 10, 11);
        for (OrderResp order : orders) {

            // 如果订单状态是列表中，直接返回false，不显示确认收货按钮
            if (list.contains(order.getState())) {
                order.setFlag(false);
                continue;
            }
            // 处理订单状态是=3 发货在途中
            handleInvalidOrderDeliveryState(order.getId());
            List<OrderDeliveryBo> orderDeliverys = orderDeliveryService.listByOrderId(order.getId());
            // 已完成，已取消，和发货在路上
            ArrayList<OrderDelivery.State> orderStateList = new ArrayList<>();
            orderStateList.addAll(getCompleted());
            orderStateList.add(OrderDelivery.State.DELIVERY_ON_THE_WAY);


            // 发货单的状态是否都是这3种状态：已完成，已取消，和发货在路上
            boolean flag1 = orderDeliverys.stream().allMatch(x -> orderStateList.stream().map(y -> y.getCode()).collect(Collectors.toList()).contains(x.getState()));
            // 必须得有1个是发货在途
            boolean flag2 = orderDeliverys.stream().anyMatch(x->x.getState().equals(OrderDelivery.State.DELIVERY_ON_THE_WAY.getCode()));
            if (flag1 && flag2) {
                order.setFlag(true);
            } else {
                order.setFlag(false);
            }


        }

    }


    /**
     * 处理订单列表状态，是否显示确认收货按钮
     * @param id orderId
     */
    public Boolean isDelivery(Long id) {

        //  1.待付款 2.待发货  10.订单完成 11.订单取消，
        List<Integer> list = Arrays.asList(1, 2, 10, 11);
        // 先同步下吉客云数据，更新下订单状态
        try {
            jkyGetOrderDeliveryByOrderId(id);
        } catch (Exception e) {
            log.error("订单显示确认收货按钮同步吉客云失败", e);
        }
        // 拉取最新订单状态
        Order order = orderMapper.selectById(id);
        // 如果订单状态是列表中，直接返回false，不显示确认收货按钮
        if (list.contains(order.getState())) {
            return false;
        }
        // 清空无效的发货单状态为已取消
        handleInvalidOrderDeliveryState(order.getId());
        // 处理订单状态是=3 发货在途中
        List<OrderDeliveryBo> orderDeliverys = orderDeliveryService.listByOrderId(order.getId());
        // 已完成，已取消，和发货在路上
        ArrayList<OrderDelivery.State> orderStateList = new ArrayList<>();
        orderStateList.addAll(getCompleted());
        orderStateList.add(OrderDelivery.State.DELIVERY_ON_THE_WAY);


        // 发货单的状态是否都是这3种状态：已完成，已取消，和发货在路上
        boolean flag1 = orderDeliverys.stream().allMatch(x -> orderStateList.stream().map(y -> y.getCode()).collect(Collectors.toList()).contains(x.getState()));
        // 必须得有1个是发货在途
        boolean flag2 = orderDeliverys.stream().anyMatch(x->x.getState().equals(OrderDelivery.State.DELIVERY_ON_THE_WAY.getCode()));
        if (flag1 && flag2) {
            return true;
        } else {
            return false;
        }

    }


    /**
     * 组合满赠的计算
     * @param order
     * @param orderGoodsList 订单下的商品，属于组合的。已经排除掉普通商品了。
     * @param partner
     * @return
     */
    private void getGiveGoodsGroupType(OrderConfirmResp resp,Order order, List<OrderGoods> orderGoodsList, Partner partner) {
        if (CollUtil.isEmpty(orderGoodsList)) {
            return;
        }

        // 固定赠品，以组合为单位，每个组合下的固定赠品
        List<GroupsBo> groupBoList = new ArrayList<>();
        resp.setGroupGiveFixList(groupBoList);

        // 任选赠品
        List<RuleGroupGiveBo> ruleGroupGiveBoList = new ArrayList<>();
        resp.setRuleGroupGivesBoList(ruleGroupGiveBoList);

        // 没处理前组合商品的返利余额
        BigDecimal rebateAmountBefore = orderGoodsList.stream().map(x -> x.getPayRebateAmount()
                .multiply(BigDecimal.valueOf(x.getNumber()))).reduce(BigDecimal.ZERO, BigDecimal::add);


        // 每个组合下有哪些商品
        Map<Long, List<OrderGoods>> groupMap = orderGoodsList.stream().collect(Collectors.groupingBy(x -> x.getGroupId()));
        // 当前合伙人下 所有符合的规则
        List<RuleGroupGives> ruleGroupGivesList = ruleGroupGivesService.getList(partner.getId());
        // 每个组合来匹配一条规则
        for (Long groupId : groupMap.keySet()) {
            Groups groups = groupService.getById(groupId);
            // 组合下商品
            List<OrderGoods> orderGoods = groupMap.get(groupId);
            // 组合下商品明细列表
            List<Long> specIds = orderGoods.stream().map(x -> x.getGoodsSpecId()).collect(Collectors.toList());

            /*
            匹配规则，计算赠品
            组合已定，计算当前组合能 匹配上哪一条规则
             */
            for (RuleGroupGives ruleGroupGives : ruleGroupGivesList) {
                // 赠送方式 0 组合满赠； 1 组合商品满赠
                Integer ruleType = ruleGroupGives.getRuleType();
                Long ruleId = ruleGroupGives.getId();

                // 这条规则和这条组合，有没有关联关系
                if (!isRuleContainCurGroup(ruleType, groupId, ruleId, specIds)) {
                    continue;
                }
                // 是否允许返利支付，这影响商品的应付，实付，订单的钱
                // 0 不支持返利； 1 允许返利支付
                Integer isRebatePay = ruleGroupGives.getIsRebatePay();
                // 根据组合满赠规则设置的是否允许返利支付，来修正当前组合下每个商品的 返利支付钱， 余额支付钱
                changeOrderGoodsListAmount(isRebatePay, orderGoods);

                // 该组合下商品 余额支付金额  也就是实付金额
                BigDecimal groupPayBalanceAmount = orderGoods.stream().map(x -> x.getPayBalanceAmount().multiply(BigDecimal.valueOf(x.getNumber()))).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 该组合下商品 应付金额  也就是实付金额 + 返利支付
                BigDecimal groupPayAmount = orderGoods.stream().map(x -> x.getPayAmount().multiply(BigDecimal.valueOf(x.getNumber()))).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 计算组合下所有的商品累加购买数量
                Integer number = orderGoods.stream().map(x->x.getNumber()).reduce(0,Integer::sum);
                // 计算组合和规则计算下，命中的次数
                Integer frequency = calcNum(number, ruleGroupGives, groupPayAmount, groupPayBalanceAmount);
                // 如果份数小于1，换下一条规则
                if (frequency < 1) {
                    continue;
                }
                log.info("匹配到组合满赠规则{},赠送的份数是{}",ruleGroupGives,frequency);


                // 固定赠送
                if (ruleGroupGives.getGiveType().intValue() == 1) {
                    List<GiveConfig> list = giveConfigService.lambdaQuery().eq(GiveConfig::getRelationId, ruleId)
                            .eq(GiveConfig::getGroupId, groupId)
                            .eq(GiveConfig::getType, ruleType)
                            .eq(GiveConfig::getIsFix, 1)
                            .list();
                    // 如果是组合商品满赠，得按照用户下单的商品明细来获取赠品，而不是直接拿全量的赠品配置
                    if (ruleType.intValue() == 1) {
                        list = list.stream().filter(x -> specIds.contains(x.getGoodsSpecId())).collect(Collectors.toList());
                    }

                    ArrayList<OrderGiveGoods> orderGiveGoods = new ArrayList<>();
                    GroupsBo groupBo = new GroupsBo();
                    BeanUtil.copyProperties(groups,groupBo);
                    groupBo.setGroupGives(orderGiveGoods);
                    groupBoList.add(groupBo);
                    for (GiveConfig giveConfig : list) {

                        OrderGiveGoods item = new OrderGiveGoods();
                        item.setOrderId(order.getId());
                        Long giveGoodsId = giveConfig.getGiveGoodsId();
                        GiveGoods giveGoods = giveGoodsService.getById(giveGoodsId);
                        item.setGiveGoodsId(giveGoods.getId());
                        item.setGiveGoodsTitle(giveGoods.getTitle());
                        item.setGiveGoodsBarcode(giveGoods.getBarcode());
                        item.setGiveGoodsNo(giveGoods.getNo());
                        // 赠品份数
                        item.setNumber(giveConfig.getFixNumber() * frequency);
                        // 组合满赠规则ID
                        item.setRuleGroupGiveId(ruleId);
                        // 组合ID
                        item.setGroupId(groupId);
                        item.setType(2); // 组合商品类型
                        item.setId(IdWorker.getId());
                        item.setPrice(giveConfig.getPrice());
                        orderGiveGoods.add(item);
                    }
                    break;
                }
                // 任选赠送
                if (ruleGroupGives.getGiveType().intValue() == 0) {

                    List<GiveConfig> list = giveConfigService.lambdaQuery().eq(GiveConfig::getRelationId, ruleId)
                            .eq(GiveConfig::getGroupId, groupId)
                            .eq(GiveConfig::getType, ruleType)
                            .eq(GiveConfig::getIsFix, 0)
                            .list();
                    if (ruleType.intValue() == 1) {
                        // 如果是组合商品满赠，计算以用户实际下单参与的商品为准
                        list = list.stream().filter(x -> specIds.contains(x.getGoodsSpecId())).collect(Collectors.toList());
                    }
                    // 每一行赠品的最大金额，最大数量配置限制
                    for (GiveConfig giveConfig : list) {
                        if (!Objects.isNull(giveConfig.getMaxAmount())) {
                            giveConfig.setMaxAmount(giveConfig.getMaxAmount().multiply(BigDecimal.valueOf(frequency)));
                        }
                        if (!Objects.isNull(giveConfig.getMaxNumber())) {
                            giveConfig.setMaxNumber(giveConfig.getMaxNumber() * frequency);
                        }
                    }


                    // 给每个赠品搞条形码
                    List<GiveConfigBo> resList =  new ArrayList<>();
                    for (GiveConfig giveConfig : list) {
                        GiveConfigBo giveConfigBo = new GiveConfigBo();
                        BeanUtil.copyProperties(giveConfig,giveConfigBo);
                        // 设置条形码
                        Long giveGoodsId = giveConfig.getGiveGoodsId();
                        GiveGoods giveGoods = giveGoodsService.getById(giveGoodsId);
                        giveConfigBo.setBarcode(giveGoods.getBarcode());
                        giveConfigBo.setNo(giveGoods.getNo());
                        giveConfigBo.setGiveGoodsName(giveGoods.getTitle());
                        resList.add(giveConfigBo);
                    }





                    RuleGroupGiveBo ruleGroupGiveBo = new RuleGroupGiveBo();
                    ruleGroupGiveBoList.add(ruleGroupGiveBo);
                    BeanUtil.copyProperties(ruleGroupGives,ruleGroupGiveBo);
                    ruleGroupGiveBo.setGroupId(groupId);
                    ruleGroupGiveBo.setGroupName(groups.getTitle());
                    ruleGroupGiveBo.setGroupGiveGoodsFreeList(resList);


                    // 0.固定金额 1.按组合实付金额比例 2.按组合应付金额比例
                    Integer maxGiveAmountType = ruleGroupGives.getMaxGiveAmountType();
                    // 固定金额
                    if (maxGiveAmountType.intValue() == 0) {
                        ruleGroupGiveBo.setMaxRealGiveAmount(ruleGroupGives.getMaxGiveAmount());
                    }
                    // 按组合实付金额比例，不乘份数
                    if (maxGiveAmountType.intValue() == 1) {
                        ruleGroupGiveBo.setMaxRealGiveAmount(BigDecimalUtil.getPercentPrice(groupPayBalanceAmount,ruleGroupGives.getMaxGiveAmount()));
                    }
                    // 按组合应付金额比例，不乘份数
                    if (maxGiveAmountType.intValue() == 2) {
                        ruleGroupGiveBo.setMaxRealGiveAmount(BigDecimalUtil.getPercentPrice(groupPayAmount,ruleGroupGives.getMaxGiveAmount()));
                    }
                    // 固定金额 乘 循环次数
                    if (maxGiveAmountType.intValue() == 3) {
                        ruleGroupGiveBo.setMaxRealGiveAmount(ruleGroupGives.getMaxGiveAmount().multiply(BigDecimal.valueOf(frequency)));
                    }

                    // 计算组合最大赠送数量
                    Integer maxGiveNumberType = ruleGroupGives.getMaxGiveNumberType();
                    if (maxGiveNumberType.intValue() == 0) {
                        // 固定数量
                        ruleGroupGiveBo.setMaxRealGiveNumber(ruleGroupGives.getMaxGiveNumber());
                    }
                    if (maxGiveNumberType.intValue() == 1) {
                        // 固定数量 乘 循环次数
                        ruleGroupGiveBo.setMaxRealGiveNumber(ruleGroupGives.getMaxGiveNumber() * frequency);
                    }

                }
                break;

            }
        }
        // 修正订单返利金额
        BigDecimal rebateAmountAfter = orderGoodsList.stream().map(x -> x.getPayRebateAmount().multiply(BigDecimal.valueOf(x.getNumber()))).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal diff = rebateAmountBefore.subtract(rebateAmountAfter);
        order.setGoodsPayBalanceAmount(order.getGoodsPayBalanceAmount().add(diff));
        order.setGoodsPayRebateAmount(order.getGoodsPayRebateAmount().subtract(diff));
        order.setPayBalanceAmount(order.getPayBalanceAmount().add(diff));
        order.setPayRebateAmount(order.getPayRebateAmount().subtract(diff));

    }


    /**
     * 组合满赠规则是否包含当前用户下单的组合
     * @param ruleType
     * @param groupId
     * @param ruleId
     * @param specIds
     * 遍历每一个有效的组合来匹配
     *                 1- 如果是组合满赠，那么，这条规则应该和当前组合有关联关系
     *                 2- 如果是组合商品满赠，  这条规则应该和当前组合- 商品 有配置的关联关系
     *                 如此，这条规则才初步生效，待下一匹配筛选
     * @return
     */
    public Boolean isRuleContainCurGroup(Integer ruleType,Long groupId,Long ruleId,List<Long> specIds) {
        // 组合满赠
        if (ruleType.intValue() == 0) {

            // 用规则ID，组合ID，组合满赠类型去rule_relation_group_goods表查询是否有记录，来证明当前循环的规则是否有效
            RuleRelationGroupGoods one = ruleRelationGroupGoodsService.lambdaQuery()
                    .eq(RuleRelationGroupGoods::getGroupId, groupId)
                    .eq(RuleRelationGroupGoods::getType, 0)
                    .eq(RuleRelationGroupGoods::getRelationId, ruleId)
                    .one();
            if (one == null) {
                // 没找到符合条件的规则，说明这条规则对当前组合无效。
                return false;
            }

        }
        // 组合商品满赠
        if (ruleType.intValue() == 1) {
            if (CollUtil.isEmpty(specIds)) {
                return false;
            }
            // 用规则ID，组合ID，商品规格ID，组合满赠类型去rule_relation_group_goods表查询是否有记录，来证明当前循环的规则是否有效
            List<RuleRelationGroupGoods> list = ruleRelationGroupGoodsService.lambdaQuery().eq(RuleRelationGroupGoods::getGroupId, groupId)
                    .eq(RuleRelationGroupGoods::getType, 1)
                    .eq(RuleRelationGroupGoods::getRelationId, ruleId)
                    .in(RuleRelationGroupGoods::getGoodsSpecId, specIds)
                    .list();
            // 为空表示无效
            if (CollUtil.isEmpty(list)) {
                return false;
            }
        }
        return true;
    }


    /**
     * 根据组合满赠规则设置的是否允许返利支付，来修正当前组合下每个商品的 返利支付钱， 余额支付钱
     * 有的组合支持返利支付，有的组合不支持返利支付
     *                 如果不支持返利，修正下单组合商品每个的返利支付金额，余额支付金额
     *                 支持返利支付则不需要修正
     *                 每个组合的变动可能对订单的应付金额，实付金额有影响，在外层修正订单
     * @param isRebatePay
     * @param orderGoods
     * @param
     */
    public void changeOrderGoodsListAmount(Integer isRebatePay,List<OrderGoods> orderGoods) {
        // 不允许返利支付
        if (isRebatePay.intValue() == 0) {
            for (OrderGoods orderGood : orderGoods) {
                // 把每个商品的返利支付清零， 余额支付金额累加上商品的返利支付
                orderGood.setPayBalanceAmount(orderGood.getPayBalanceAmount().add(orderGood.getPayRebateAmount()));
                // 订单下返利的修正金额计算
                orderGood.setPayRebateAmount(BigDecimal.ZERO);
            }

        }
    }


    /**
     * 计算组合和规则计算下，命中的次数
     * @param number 组合下的所有商品数量累加
     * @param ruleGroupGives
     * @param groupPayAmount 组合应付金额
     * @param groupPayBalanceAmount 组合实付金额
     * @return
     */
    public Integer calcNum(Integer number,RuleGroupGives ruleGroupGives,BigDecimal groupPayAmount,BigDecimal groupPayBalanceAmount) {
        // 触发条件 1.价格、2.数量
        RuleTriggerConditionEnum conditionEnum = RuleTriggerConditionEnum.getEnum(ruleGroupGives.getTriggerCondition());
        // 规则触发频率 1.每满、2.满 ; 每满为多次叠加，满为仅一次
        RuleTriggerFrequencyEnum frequencyEnum = RuleTriggerFrequencyEnum.getEnum(ruleGroupGives.getTriggerFrequency());
        // 次数/份数
        int frequency = 1;


        // 数量份数计算
        if (RuleTriggerConditionEnum.NUMBER.equals(conditionEnum)) {
            //  满 仅一次 [最小数量,最大数量)   每满 多次叠加 仅最小数量
            frequency = frequencyEnum.equals(RuleTriggerFrequencyEnum.FULL)
                    ? (number >= ruleGroupGives.getMinNumber() && number < ruleGroupGives.getMaxNumber() ? 1 : 0)
                    // 每满 数量 必须要满足门槛数量
                    : (number >= ruleGroupGives.getThresholdNumber() ? (number / ruleGroupGives.getMinNumber()) : 0);
        }

        // 价格份数计算
        if (RuleTriggerConditionEnum.PRICE.equals(conditionEnum)) {
            // 触发金额类型: 1.实付金额(余额支付部分) 2.应付金额
            RuleTriggerAmountTypeEnum amountTypeEnum = RuleTriggerAmountTypeEnum.getEnum(ruleGroupGives.getTriggerAmountType());

            // 参与比较的金额
            BigDecimal compareAmount = RuleTriggerAmountTypeEnum.PAY_AMOUNT.equals(amountTypeEnum) ? groupPayAmount : groupPayBalanceAmount;
            // 满 仅一次 [最小金额,最大金额);  每满 多次叠加 仅最小金额
            frequency = frequencyEnum.equals(RuleTriggerFrequencyEnum.FULL)
                    ? (compareAmount.compareTo(ruleGroupGives.getMinAmount()) >= 0 && compareAmount.compareTo(ruleGroupGives.getMaxAmount()) < 0 ? 1 : 0)
                    : (compareAmount.compareTo(ruleGroupGives.getThresholdAmount()) >= 0 ? compareAmount.divideToIntegralValue(ruleGroupGives.getMinAmount()).intValue() : 0);
        }
        return frequency;
    }


    /**
     * 追加
     * @param order
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean edit(Order order,String userName) {
        if (order.getId() == null || StrUtil.isBlank(order.getSellerNotes())) {
            throw new AlertException("订单ID或商家备注不能为空");
        }
        Order orderFromDb = getById(order.getId());
        String oldNotes = orderFromDb.getSellerNotes();
        String ext = userName +  "--" + getNowTime();
        // 新的
        String newNotes = oldNotes + "|" + order.getSellerNotes() + "(" + ext + ")";
        order.setSellerNotes(newNotes);
        // 将商家备注同步到吉客云
        List<OrderDelivery> orderDeliveryList = orderDeliveryService.lambdaQuery().eq(OrderDelivery::getOrderId, order.getId()).eq(OrderDelivery::getIsJkyCreate, 0).list();
        for (OrderDelivery orderDelivery : orderDeliveryList) {
            jackYunService.sellerNotesToJKY(orderDelivery.getJyNo(), newNotes);
        }
        return updateById(order);
    }


    /**
     * 处理订单业务员
     * @param confirmResp
     * @param partner
     * @param
     */
    public void handlerExt(OrderConfirmResp confirmResp,Partner partner,Integer  tradeType,String orderNotes) {
        // 设置发货单业务员
        User user = userService.getById(partner.getBusinessId());
        // 设置订单业务员，订单类型，订单备注，商家备注
        Order order = confirmResp.getOrder();
        order.setBusinessName(user.getUsername());
        order.setPartnerName(partner.getTitle());
        order.setTradeType(tradeType);
        order.setOrderNotes(orderNotes);
    }


    public String getNowTime() {
        LocalDateTime currentTime = LocalDateTime.now();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        String formattedTime = currentTime.format(formatter);
        return formattedTime;
    }

    /**
     * 取消订单的时候，手动拉一下最新的单子
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelJkyGetOrderDeliveryByOrderId(Long orderId) {
        Order order = getById(orderId);
        Order.State state = Order.State.getEnum(order.getState());

        // 待付款直接跳出
        if (Order.State.WAIT_PAY.equals(state)) {
            return;
        }

        // 仅在待发货，待收货下拉取数据
        if (!Order.State.WAIT_SHIP.equals(state) && !Order.State.WAIT_RECEIVE.equals(state)) {
            log.error("订单号:{},当前状态:{},仅已付款、待发货状态下拉取数据", order.getId(), state.getDesc());
            throw new AlertException("订单状态不正确");
        }
        // 所有的发货单信息
        List<OrderDelivery> syncBeforeDeliveries = orderDeliveryService.lambdaQuery().eq(OrderDelivery::getOrderId, orderId).list();
        // 批量查询吉客云订单信息
        jackYunService.syncOrder(order, syncBeforeDeliveries);


    }

    /**
     * 根据总瓶数和每箱的瓶数，计算箱数和余数瓶数，并以"n箱n瓶"的格式返回结果。
     *
     * @param totalBottles 总瓶数
     * @param boxesPerBox  每箱的瓶数
     * @return 格式化的字符串
     */
    public static String calculateBoxAndBottle(int totalBottles, int boxesPerBox) {
        if (boxesPerBox == 0) {
            return totalBottles+"瓶";
        }
        // 计算箱数
        int boxes = totalBottles / boxesPerBox;
        // 计算余数瓶数
        int bottles = totalBottles % boxesPerBox;

        // 使用StringBuilder来构建字符串，因为StringBuilder比String在拼接时更高效
        StringBuilder sb = new StringBuilder();
        sb.append(boxes).append("箱");
        if (bottles > 0) { // 如果余数不为0，则添加瓶数
            sb.append(bottles).append("瓶");
        }

        // 将StringBuilder转换成String并返回
        return sb.toString();
    }


    /**
     * 返回订单类型=售后的个数
     * @return
     */
    public Integer getSaleCount(Long partnerId) {
        return orderMapper.getOrderIdInAfterList(partnerId).size();
    }

    /**
     * 将无效的订单状态设置为5010
     * @param orderId
     */
    public void handleInvalidOrderDeliveryState(Long orderId) {
        List<String> jyList = orderMapper.handleInvalidOrderDeliveryState(orderId);
        if (CollUtil.isNotEmpty(jyList)) {
            // 将这些发货单的状态更新为5010
            orderDeliveryService.lambdaUpdate()
                    .in(OrderDelivery::getJyNo, jyList) // 指定要更新的记录的条件：jy_no 在 jyList 中
                    .set(OrderDelivery::getState, 5010) // 设置要更新的字段和值：state = 5010L
                    .update();
        }
    }


    /**
     * 再来一单
     * @param user
     * @param orderId
     */
    public void onMoreOrder(User user, Long orderId) {

        List<OrderGoods> orderGoodsList = orderMapper.getGoodsSpecAndNumberListByIdAndUserId(user.getId(), orderId);
        for (OrderGoods orderGoods : orderGoodsList) {
            Long goodsSpecId = orderGoods.getGoodsSpecId();
            Integer numberOrderGoods = orderGoods.getNumber();
            Integer numberShoppingCart = shoppingCartMapper.getNumberByUserIdAndGoodsSpecId(user.getId(), goodsSpecId);
            Integer number = numberShoppingCart == null ? numberOrderGoods : numberOrderGoods + numberShoppingCart;
            shoppingCartService.addCart(user.getId(), user.getPartnerId(), goodsSpecId, number);
            shoppingCartMapper.updateIsCheckAndIsOneMore(user.getId(), goodsSpecId);
        }

    }

    /**
     * 订单发货单批量导出
     */
    public void export(HttpServletResponse response, Order dto, LocalDateTimeRange createdAtRange, OrdersQuery query) throws IOException {
        QueryWrapper<Order> wrapper = MbQueryLib.getQueryWrapper(dto);

        if (createdAtRange != null) {
            wrapper.between("created_at", createdAtRange.getStart(), createdAtRange.getEnd());
        }
        if (ObjectUtil.isNotNull(query.getBusinessId())) {
            // 查询这个业务员下合伙人
            List<Long> partnerIdList = partnerService.lambdaQuery().eq(Partner::getBusinessId, query.getBusinessId()).list().stream().map(BaseEntity::getId).collect(Collectors.toList());
            partnerIdList.add(-1L);
            wrapper.in("partner_id", partnerIdList);
        }

        wrapper.ge(query.getPayAmountRangeMin() != null, "pay_amount", query.getPayAmountRangeMin());
        wrapper.le(query.getPayAmountRangeMax() != null, "pay_amount", query.getPayAmountRangeMax());

        if (query.getDeliveryId() != null) {
            wrapper.exists("select 1 from order_deliveries where order_deliveries.order_id = orders.id and order_deliveries.id = {0}", query.getDeliveryId());
        }
        if (CharSequenceUtil.isNotBlank(query.getJyNo())) {
            wrapper.exists("select 1 from order_deliveries where order_deliveries.order_id = orders.id and order_deliveries.jy_no = {0}", query.getJyNo());
        }
        if (CollectionUtil.isNotEmpty(query.getStateList())) {
            wrapper.in("state", query.getStateList());
        }

        // 是否售后的查询条件
        if (ObjectUtil.isNotNull(query.getIsAfterSale())) {
            String afterSaleSubQuery =
                    "select 1 from order_deliveries od " +
                            "where od.order_id = orders.id " +
                            "and (" +
                            "exists (select 1 from exchange_delivery ed where ed.delivery_id = od.id and ed.audit_state = 1) " +
                            "or exists (select 1 from reimbursement_refunds rr where rr.delivery_id = od.id and rr.audit_state = 1) " +
                            "or exists (select 1 from wrong_miss_delivery wmd where wmd.delivery_id = od.id and wmd.audit_state = 1)" +
                            ")";
            if (query.getIsAfterSale() == 1) {
                wrapper.exists(afterSaleSubQuery);
            } else {
                wrapper.notExists(afterSaleSubQuery);
            }
        }

        // 售后单号查询条件
        if (CharSequenceUtil.isNotBlank(query.getNo())) {
            wrapper.exists(
                    "select 1 from order_deliveries od " +
                            "where od.order_id = orders.id " +
                            "and (" +
                            "exists (select 1 from exchange_delivery ed where ed.delivery_id = od.id and ed.return_change_no = {0}) " +
                            "or exists (select 1 from reimbursement_refunds rr where rr.delivery_id = od.id and rr.refund_no = {0}) " +
                            "or exists (select 1 from wrong_miss_delivery wmd where wmd.delivery_id = od.id and wmd.disorder_no = {0})" +
                            ")", query.getNo());
        }

        // 售后审批状态查询条件
        if (ObjectUtil.isNotNull(query.getAuditState())) {
            wrapper.exists(
                    "select 1 from order_deliveries od " +
                            "where od.order_id = orders.id " +
                            "and (" +
                            "exists (select 1 from exchange_delivery ed where ed.delivery_id = od.id and ed.audit_state = {0}) " +
                            "or exists (select 1 from reimbursement_refunds rr where rr.delivery_id = od.id and rr.audit_state = {0}) " +
                            "or exists (select 1 from wrong_miss_delivery wmd where wmd.delivery_id = od.id and wmd.audit_state = {0})" +
                            ")", query.getAuditState());
        }

        List<Long> orderIds = orderMapper.selectList(wrapper).stream()
                .map(Order::getId)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(orderIds)) {
            throw new AlertException("无符合筛选条件的订单");
        }
        if (orderIds.size() > 100) {
            throw new AlertException("订单数超过100，数据量太大");
        }
        List<OrdersDto> ordersDtoList = orderMapper.getOrderAndOrderDeliveryByOrderId(orderIds);

        for (OrdersDto ordersDto : ordersDtoList) {
            ordersDto.setReceiverAddress(); // 调用方法设置 ReceiverAddress
        }

        Workbook workBook = new XSSFWorkbook();

        Sheet sheet = workBook.createSheet("发货单导出");
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        Row headerRow = sheet.createRow(0);
        String[] columns = {"订单号", "合伙人", "同步状态", "订单状态", "原价", "折扣", "应付", "返利支付", "余额支付", "支付时间", "业务员", "发货单编号", "吉客云单号", "吉客云状态", "同步吉客云", "创建方式", "收货人", "收获人电话", "收货地址", "物流公司", "物流单号", "发货单创建时间", "商品编号", "商品条码", "商品名称", "规格", "数量", "应付单价", "应付总金额", "备注"};
        for (int i = 0; i < columns.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(columns[i]);
        }
        int rowNum = 1;
        for (OrdersDto ordersDto : ordersDtoList) {
            Row row = sheet.createRow(rowNum++);

            row.createCell(0).setCellValue(ordersDto.getOrderId().toString());
            row.createCell(1).setCellValue(ordersDto.getPartnerName());

            String isSyncErrorStr = OrdersDto.IsSyncError.getEnum(ordersDto.getIsSyncError()).getDesc();
            row.createCell(2).setCellValue(isSyncErrorStr);

            String orderStateStr = OrdersDto.OrderState.getEnum(ordersDto.getOrderState()).getDesc();
            row.createCell(3).setCellValue(orderStateStr);

            row.createCell(4).setCellValue(ordersDto.getOriginalAmount().toString());
            row.createCell(5).setCellValue(ordersDto.getDiscountAmount().toString());
            row.createCell(6).setCellValue(ordersDto.getPayAmount().toString());
            row.createCell(7).setCellValue(ordersDto.getPayRebateAmount().toString());
            row.createCell(8).setCellValue(ordersDto.getPayBalanceAmount().toString());

            Cell cell = row.createCell(9);
            LocalDateTime payTime = ordersDto.getPayTime();
            if (payTime != null){
                // 将 LocalDateTime 转换为字符串
                String formattedPayTime = payTime.format(formatter);
                cell.setCellValue(formattedPayTime);
            }

            row.createCell(10).setCellValue(ordersDto.getBusinessName());
            row.createCell(11).setCellValue(ordersDto.getOrderDeliveryId().toString());
            row.createCell(12).setCellValue(ordersDto.getJyNo());

            String orderDeliveryStateStr = OrdersDto.OrderDeliveryState.getEnum(ordersDto.getOrderDeliveryState()).getDesc();
            row.createCell(13).setCellValue(orderDeliveryStateStr);

            String isSyncStr = OrdersDto.IsSync.getEnum(ordersDto.getIsSync()).getDesc();
            row.createCell(14).setCellValue(isSyncStr);

            String isJkyCreateStr = OrdersDto.IsJkyCreate.getEnum(ordersDto.getIsJkyCreate()).getDesc();
            row.createCell(15).setCellValue(isJkyCreateStr);

            row.createCell(16).setCellValue(ordersDto.getReceiverName());
            row.createCell(17).setCellValue(ordersDto.getReceiverPhone());
            row.createCell(18).setCellValue(ordersDto.getReceiverAddress());
            row.createCell(19).setCellValue(ordersDto.getLogisticCompany());
            row.createCell(20).setCellValue(ordersDto.getLogisticNo());

            Cell cell2 = row.createCell(21);
            LocalDateTime orderDeliveryCreatedAt = ordersDto.getOrderDeliveryCreatedAt();
            if (orderDeliveryCreatedAt != null){
                // 将 LocalDateTime 转换为字符串
                String formattedOrderDeliveryCreatedAt = orderDeliveryCreatedAt.format(formatter);
                cell2.setCellValue(formattedOrderDeliveryCreatedAt);
            }

            row.createCell(22).setCellValue(ordersDto.getGoodNo());
            row.createCell(23).setCellValue(ordersDto.getBarcode());
            row.createCell(24).setCellValue(ordersDto.getGoodsName());
            row.createCell(25).setCellValue(ordersDto.getSpecName());
            row.createCell(26).setCellValue(ordersDto.getNumber().toString());
            row.createCell(27).setCellValue(ordersDto.getPayAmountUnitPrice().toString());
            row.createCell(28).setCellValue(ordersDto.getTotalAmount().toString());
            row.createCell(29).setCellValue(ordersDto.getNotes());
        }
        // 开始导出，导出头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("发货单导出", "utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName+".xls");

        BufferedOutputStream out = new BufferedOutputStream(response.getOutputStream());
        workBook.write(out);
        out.flush();
        workBook.close();


    }
}


