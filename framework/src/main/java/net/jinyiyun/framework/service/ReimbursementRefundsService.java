package net.jinyiyun.framework.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.jinyiyun.common.lib.MbQueryLib;
import net.jinyiyun.common.security.Auth;
import net.jinyiyun.common.utils.BigDecimalUtil;
import net.jinyiyun.common.utils.JacksonUtil;
import net.jinyiyun.config.exception.AlertException;
import net.jinyiyun.config.request.PageReq;
import net.jinyiyun.framework.common.enums.TrueOrFalseEnum;
import net.jinyiyun.framework.dto.OrderDeliveryDetailDto;
import net.jinyiyun.framework.dto.ReimbursementRefundsGoodsDto;
import net.jinyiyun.framework.dto.aftersale.ReimbursementRefundsDto;
import net.jinyiyun.framework.entity.*;
import net.jinyiyun.framework.order.enums.AfterSaleStatusEnum;
import net.jinyiyun.framework.order.mapper.ReimbursementRefundsMapper;
import net.jinyiyun.framework.service.jackyun.JackYunLib;
import net.jinyiyun.framework.service.jackyun.JackYunService;
import net.jinyiyun.framework.service.jackyun.dto.JackYunResponse;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static net.jinyiyun.framework.service.jackyun.JackYunLib.REIMBURSEMENTREFUNDS_PAGE;
import static net.jinyiyun.framework.service.jackyun.JackYunLib.SUCCESS_CODE;

/**
* <AUTHOR>
* @description 针对表【reimbursement_refunds(补偿退款单表)】的数据库操作Service实现
* @createDate 2024-06-26 17:22:26
*/
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class ReimbursementRefundsService extends ServiceImpl<ReimbursementRefundsMapper, ReimbursementRefunds>
{

    private final ReimbursementRefundsGoodsService reimbursementRefundsGoodsService;

    private final JackYunService jackYunService;

    private final JackYunLib jackYunLib;

    private final OrderDeliveryService orderDeliveryService;

    private final PartnerService partnerService;


    /**
     * 分页查询
     * @param pageDto
     * @param dto
     * @return
     */
    public Page<ReimbursementRefunds> index(PageReq pageDto, ReimbursementRefundsDto dto) {
        QueryWrapper<ReimbursementRefunds> queryWrapper = MbQueryLib.getQueryWrapper(dto);
        // 分页排除掉草稿状态
        queryWrapper.ne("audit_state",0);
        // 根据商品编码搜索
        if (ObjectUtil.isNotNull(dto) && ObjectUtil.isNotNull(dto.getNo())) {
            queryWrapper.exists("select 1 from reimbursement_refunds_goods where reimbursement_refunds_goods.reimbursement_refunds_id = reimbursement_refunds.id and reimbursement_refunds_goods.goods_no = {0}",dto.getNo());
        }
        return this.page(pageDto.toMpPage(), queryWrapper);
    }


    @Transactional(rollbackFor = Exception.class)
    public void store(ReimbursementRefundsDto dto) {
        // 判断商品自定义的余额扣除加返利扣除是否大于应退合计
        for (ReimbursementRefundsGoodsDto good : dto.getGoods()) {
            if (good.getAmountDeductionType() == 1 && good.getBalanceDeduction().add(good.getRebateDeduction()).compareTo(good.getRefundFee()) > 0) {
                throw new AlertException("余额扣除加返利扣除不能大于应退合计");
            }
            if (good.getAmountDeductionType() == 0 && (ObjectUtil.isNull(good.getPayAmount()) || ObjectUtil.isNull(good.getDiscountAmount()))) {
                throw new AlertException("未选择发货单商品");
            }
        }

        OrderDelivery orderD = orderDeliveryService.getById(dto.getDeliveryId());
        dto.setOrderId(orderD.getOrderId());
        // 合伙人
        dto.setPartnerId(orderD.getPartnerId());

        List<OrderDeliveryDetailDto> orderDeliveryGoods =
                orderDeliveryService.getOrderDeliveryDetailByDeliveryId(dto.getDeliveryId());
        // 草稿，直接保存，无需校验
        if (dto.getEditType() == 0) {
            clearBefore(dto.getDeliveryId());
            dto.setAuditState(0);
            dto.setProcessState(0);

            save(dto);
            Long id = dto.getId();
            List<ReimbursementRefundsGoodsDto> goods = dto.getGoods();
            List<ReimbursementRefundsGoods> reimbursementRefundsGoods = goods.stream().map(x -> {
                ReimbursementRefundsGoods good = new ReimbursementRefundsGoods();
                BeanUtil.copyProperties(x, good);
                good.setReimbursementRefundsId(id);
                return good;
            }).collect(Collectors.toList());
            dto.setNeedPartnerAudit(needPartnerAudit(reimbursementRefundsGoods, orderDeliveryGoods));
            reimbursementRefundsGoodsService.saveBatch(reimbursementRefundsGoods);
        }
        // 正式提交
        if (dto.getEditType() == 1) {
            dto.setAuditState(3);
            dto.setProcessState(0);
            saveNext(dto, orderDeliveryGoods);

        }
        // 草稿且提交，清空此人所有草稿和草稿下货品
        if (dto.getEditType() == 2) {
            dto.setAuditState(3);
            dto.setProcessState(0);
            // 先清除当前用户下所有草稿及货品
            clearBefore(dto.getDeliveryId());
            saveNext(dto, orderDeliveryGoods);
        }
    }


    /**
     * 清除之前售后单及下面货品
     */
    public void clearBefore(Long deliveryId) {
        Long userId = 0L;
        try {
            Admin user = Auth.getUser();
            userId = user.getId();
        } catch (Exception e) {
            User user2 = Auth.getUser();
            userId = user2.getId();
        }
        List<Long> collect = this.lambdaQuery().eq(ReimbursementRefunds::getAuditState, 0)
                .eq(ReimbursementRefunds::getCreater, userId)
                .eq(ReimbursementRefunds::getDeliveryId, deliveryId)
                .list().stream().map(x -> x.getId()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(collect)) {
            this.removeBatchByIds(collect);
            reimbursementRefundsGoodsService.lambdaUpdate().in(ReimbursementRefundsGoods::getReimbursementRefundsId,collect).remove();
        }
    }


    public void saveNext(ReimbursementRefundsDto dto, List<OrderDeliveryDetailDto> orderDeliveryGoods) {
        OrderDelivery orderDelivery = orderDeliveryService.getById(dto.getDeliveryId());
        // 网店订单号
        dto.setOnlineTradeNo(orderDelivery.getId().toString());
        save(dto);
        Long id = dto.getId();
        List<ReimbursementRefundsGoodsDto> goods = dto.getGoods();

        // 计算商品金额拆分
        for (ReimbursementRefundsGoodsDto good : goods) {
            good.setReimbursementRefundsId(id);
            // 如果等于原来比例，则根据比例计算出该商品返利支付部分，和余额支付部分
            if (good.getAmountDeductionType() == 0) {
                // 如果销售单价等于原销售单价（即销售单价未改变），则余额部分为商品余额支付价*数量，返利部分为商品返利支付价*数量
                if (good.getPrice().compareTo(good.getPayAmount().add(good.getDiscountAmount())) == 0) {
                    good.setBalanceDeduction(good.getPayAmount().multiply(good.getSellCount()));
                    good.setRebateDeduction(good.getDiscountAmount().multiply(good.getSellCount()));
                } else {
                    // 如果销售单价被改变，则计算余额支付单价和实际商品总单价比例 被除数扩大100倍 结果取值 [0,100] 保留两位小数（向下取整）
                    // 来重新按比例计算余额金额和返利金额
                    BigDecimal proportion = BigDecimalUtil.divide(BigDecimalUtil.multiply(good.getPayAmount(),
                            new BigDecimal(100)), good.getPayAmount().add(good.getDiscountAmount()));
                    // 余额金额
                    good.setBalanceDeduction(BigDecimalUtil.getPercentPrice(good.getRefundFee(), proportion));
                    // 返利金额
                    good.setRebateDeduction(good.getRefundFee().subtract(good.getBalanceDeduction()));
                }
            }
        }
        // 将传参dto封装补偿退款单详情并保存
        List<ReimbursementRefundsGoods> reimbursementRefundsGoods = goods.stream().map(x -> {
            ReimbursementRefundsGoods good = new ReimbursementRefundsGoods();
            BeanUtil.copyProperties(x, good);
            return good;
        }).collect(Collectors.toList());
        dto.setNeedPartnerAudit(needPartnerAudit(reimbursementRefundsGoods, orderDeliveryGoods));
        reimbursementRefundsGoodsService.saveBatch(reimbursementRefundsGoods);

        // 售后单余额部分
        BigDecimal balance = goods.stream()
                .map(ReimbursementRefundsGoods::getBalanceDeduction)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        dto.setBalanceDeduction(balance);
        BigDecimal rebate = goods.stream()
                .map(ReimbursementRefundsGoods::getRebateDeduction)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        dto.setRebateDeduction(rebate);
        // 售后商品数量
        BigDecimal allCount = goods.stream().filter(x -> x.getGoodsType() == 0).map(x -> x.getSellCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        dto.setGoodsCount(allCount);
        updateById(dto);
    }


    @Transactional(rollbackFor = Exception.class)
    public void submit(Long id) {
        // 根据ID查询出售后单
        ReimbursementRefunds reimbursementRefunds = getById(id);
        // 设置审核状态为审核中
        reimbursementRefunds.setAuditState(3);
        updateById(reimbursementRefunds);

    }


    @Transactional(rollbackFor = Exception.class)
    public void cancelSubmitDing(Long id) {

        // 根据ID查询出售后单
        ReimbursementRefunds reimbursementRefunds = getById(id);
        // 设置审核状态为草稿
        reimbursementRefunds.setAuditState(0);
        updateById(reimbursementRefunds);
    }



    /**
     * 售后单下的货品分页
     * 也可根据售后单ID，查询下面的货品列表
     * @param pageDto
     * @param dto
     * @return
     */
    public Page<ReimbursementRefundsGoods> goodsIndex(PageReq pageDto, ReimbursementRefundsGoods dto) {
        QueryWrapper<ReimbursementRefundsGoods> queryWrapper = MbQueryLib.getQueryWrapper(dto);
        return reimbursementRefundsGoodsService.page(pageDto.toMpPage(), queryWrapper);
    }


    /**
     * 审核通过/拒绝
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void auditPass(Long id,Integer state) {

        ReimbursementRefunds reimbursementRefunds = getById(id);
        // 审核通过
        if (AfterSaleStatusEnum.APPROVED.getCode().equals(state) && TrueOrFalseEnum.FALSE.getCode().equals(reimbursementRefunds.getNeedPartnerAudit())) {
            reimbursementRefunds.setAuditState(1);
            reimbursementRefunds.setProcessState(1);
            updateById(reimbursementRefunds);
            // 推送到吉客云
            try {
                push2JKY(id,reimbursementRefunds);
            } catch (Exception e) {
                log.info("补偿退款单审核通过推送到吉客云异常{}",reimbursementRefunds,e);
            }
            // 钱退到账户
            handleMoney(reimbursementRefunds);
        } else if (AfterSaleStatusEnum.APPROVED.getCode().equals(state) && TrueOrFalseEnum.TRUE.getCode().equals(reimbursementRefunds.getNeedPartnerAudit())) {
            reimbursementRefunds.setAuditState(AfterSaleStatusEnum.PARTNER_AUDITING.getCode());
            updateById(reimbursementRefunds);
        }
        // 审核拒绝
        if (2 == state) {
            reimbursementRefunds.setAuditState(2);
            reimbursementRefunds.setProcessState(1);
            updateById(reimbursementRefunds);
        }
    }


    /**
     * 钉钉审批通过后，返利和余额退到合伙人的钱包
     */
    public void handleMoney(ReimbursementRefunds dto) {

        OrderDelivery orderDelivery = orderDeliveryService.getById(dto.getDeliveryId());
        // 合伙人ID
        Long partnerId = orderDelivery.getPartnerId();
        // 订单ID
        Long orderId = orderDelivery.getOrderId();
        // 退余额
        if (dto.getBalanceDeduction().compareTo(BigDecimal.ZERO) > 0) {
            // 退余额
            partnerService.addBalance(partnerId, dto.getBalanceDeduction(), "售后余额退款", ReimbursementRefunds.class,
                    PartnerPayment.RelationType.BALANCE_AFTER_SALE_REFUND, dto.getId(), orderId, null, null);
        }
        // 退返利
        if (dto.getRebateDeduction().compareTo(BigDecimal.ZERO) > 0) {
            partnerService.addRebateBalance(partnerId,dto.getRebateDeduction(),"售后返利退款",ReimbursementRefunds.class, PartnerPayment.RelationType.REBATE_AFTER_SALE_REFUND,dto.getId(),orderId,null);
        }
    }


    /**
     * 根据no拉取补偿退款单列表
     * @param no
     */
    public void syncRePageFromJKY(String no,Long id) {
        HashMap<String, Object> biz = new HashMap<>();
        biz.put("refundNoList", Arrays.asList(no));

        JackYunResponse response = jackYunLib.sendRequest(REIMBURSEMENTREFUNDS_PAGE, biz);

        if (!response.getCode().equals(SUCCESS_CODE)) {
            log.error("拉取补偿退款单失败：{}", response.getMsg());
            return;
        }

        JSONArray trades = JSONUtil.parseObj(response.getResult().getData()).getJSONArray("refundList");
        log.info("拉取补偿退款单结果：{}", trades);
        if (CollUtil.isEmpty(trades)) {
            log.info("拉取补偿退款单结果为空");
            return;
        }
        trades.forEach(trade -> {
            ReimbursementRefunds reimbursementRefunds = JacksonUtil.str2obj(trade.toString(), ReimbursementRefunds.class);
            // 获取网店售后单号，和货品摘要
            String goodslist = reimbursementRefunds.getGoodslist();
            this.lambdaUpdate()
                    .set(ReimbursementRefunds::getGoodslist,goodslist)
                    .set(ReimbursementRefunds::getRefundNo,no)
                    .eq(ReimbursementRefunds::getId,id)
                    .update();

        });
    }



    public void push2JKY(Long id,ReimbursementRefunds reimbursementRefunds) {
        List<ReimbursementRefundsGoods> list = reimbursementRefundsGoodsService.lambdaQuery().eq(ReimbursementRefundsGoods::getReimbursementRefundsId, id).list();
        // 推送吉客云
        String refundNo = jackYunService.createReimbursementRefunds(reimbursementRefunds, list);
        if (StrUtil.isNotBlank(refundNo) &&refundNo.startsWith("TK")) {
            // tk开头表示推送到吉客云成功
            reimbursementRefunds.setSyncStatus(1);
            reimbursementRefunds.setErrorReason("同步成功");
            log.info("推送补偿退款单成功生成的单号：{}", refundNo);
            // 再次远程调用吉客云拿网店售后单号，货品摘要
            this.syncRePageFromJKY(refundNo, id);
        } else {
            // 推送吉客云失败
            reimbursementRefunds.setSyncStatus(2);
            reimbursementRefunds.setErrorReason(refundNo);
        }
        updateById(reimbursementRefunds);

    }


    /**
     * 判断单价是否发生了变化
     */
    private Integer needPartnerAudit(List<ReimbursementRefundsGoods> exchangeDeliveryGoods,
                                     List<OrderDeliveryDetailDto> orderDeliveryGoods) {
        if (CollUtil.isEmpty(orderDeliveryGoods) || CollUtil.isEmpty(exchangeDeliveryGoods)) {
            return TrueOrFalseEnum.FALSE.getCode();
        }
        Map<String, BigDecimal> originalPrice =
                orderDeliveryGoods.stream().collect(Collectors.toMap(OrderDeliveryDetailDto::getBarcode,
                        OrderDeliveryDetailDto::getPrice));
        boolean present = exchangeDeliveryGoods.stream().anyMatch(a -> {
            String barcode = a.getBarcode();
            BigDecimal price = a.getPrice();
            BigDecimal original = originalPrice.get(barcode);
            return original != null && original.compareTo(price) != 0;
        });
        if (present) {
            return TrueOrFalseEnum.TRUE.getCode();
        } else {
            return TrueOrFalseEnum.FALSE.getCode();
        }
    }


}
