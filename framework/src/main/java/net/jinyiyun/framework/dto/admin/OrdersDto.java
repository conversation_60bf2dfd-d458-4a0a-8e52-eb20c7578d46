package net.jinyiyun.framework.dto.admin;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单和发货单
 *
 */

@Data
@NoArgsConstructor
public class OrdersDto{


    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 合伙人
     */
    private String partnerName;

    /**
     * 同步状态
     */
    private Integer isSyncError;

    /**
     * 订单状态
     */
    private Integer orderState;

    /**
     * 原价
     */
    private BigDecimal originalAmount;

    /**
     * 折扣
     */
    private BigDecimal discountAmount;

    /**
     * 应付
     */
    private BigDecimal payAmount;

    /**
     * 返利支付
     */
    private BigDecimal payRebateAmount;

    /**
     * 余额支付
     */
    private BigDecimal payBalanceAmount;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 业务员
     */
    private String businessName;

    /**
     * 发货单id
     */
    private Long orderDeliveryId;

    /**
     * 吉客云单号
     */
    private String jyNo;

    /**
     * 吉客云状态
     */
    private Integer orderDeliveryState;

    /**
     * 同步吉客云
     */
    private Integer isSync;

    /**
     * 创建方式
     */
    private Integer isJkyCreate;

    /**
     * 收货人
     */
    private String receiverName;

    /**
     * 收货电话
     */
    private String receiverPhone;

    /**
     * 省份
     */
    private String provinceTitle;

    /**
     * 城市
     */
    private String cityTitle;

    /**
     * 区县
     */
    private String districtTitle;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 收货地址
     */
    private String receiverAddress;

    /**
     * 物流公司
     */
    private String logisticCompany;

    /**
     * 物流单号
     */
    private String logisticNo;

    /**
     * 发货单创建时间
     */
    private LocalDateTime orderDeliveryCreatedAt;

    /**
     * 商品编号
     */
    private String goodNo;

    /**
     * 商品条码
     */
    private String barcode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 规格
     */
    private String specName;

    /**
     * 数量
     */
    private Integer number;

    /**
     * 应付单价
     */
    private BigDecimal payAmountUnitPrice;

    /**
     * 应付总金额
     */
    private BigDecimal totalAmount;

    /**
     * 备注
     */
    private String notes;


    public void setReceiverAddress() {
        this.receiverAddress = provinceTitle + cityTitle + districtTitle + address;
    }


    /**
     * 订单状态
     */
    public enum OrderState {
        /**
         * 待付款
         */
        WAIT_PAY(1, "待付款"),
        /**
         * 待发货
         */
        WAIT_SHIP(2, "待发货"),
        /**
         * 待收货/已发货
         */
        WAIT_RECEIVE(3, "待收货/已发货"),
        /**
         * 订单完成
         */
        COMPLETE(10, "订单完成"),
        /**
         * 订单取消
         */
        CANCEL(11, "订单取消");

        private final Integer code;
        private final String desc;

        OrderState(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static OrderState getEnum(Integer code) {
            for (OrderState orderState : OrderState.values()) {
                if (orderState.getCode().equals(code)) {
                    return orderState;
                }
            }
            throw new IllegalArgumentException("无效的订单状态");
        }
    }

    /**
     * 同步状态
     */
    public enum IsSyncError {
        /**
         * 正常
         */
        NORMAL(0, "正常"),
        /**
         * 异常
         */
        ABNORMAL(1, "异常");


        private final Integer code;
        private final String desc;

        IsSyncError(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static IsSyncError getEnum(Integer code) {
            for (IsSyncError isSyncError : IsSyncError.values()) {
                if (isSyncError.getCode().equals(code)) {
                    return isSyncError;
                }
            }
            throw new IllegalArgumentException("无效的同步状态");
        }
    }

    /**
     * 吉客云状态
     */
    public enum OrderDeliveryState {
        TEST0(0, "0"),
        TEST10(10, "10"),
        WAIT_AUDIT(1010, "待审核"),
        AUDITING(1020, "审核中"),
        PRE_SALE(1030, "预售"),
        WAIT_REVIEW(1050, "待复核"),
        WAIT_STOCK(2000, "备货等待"),
        WAIT_STOCK_REPLENISHMENT(2010, "备货等待等补货"),
        WAIT_SERVICE(2020, "服务等待"),
        WAIT_STOCK_PRODUCTION(2030, "备货等待等生产"),
        WAIT_PURCHASE(2040, "采购等待"),
        VIRTUAL_DELIVERY(3010, "虚拟发货"),
        WAIT_DELIVERY(4110, "待发货待递交"),
        DELIVERY_ING(4111, "待发货递交中"),
        DELIVERY_ED(4112, "待发货已递交"),
        DELIVERY_FAIL(4113, "待发货-递交失败"),
        DELIVERY_CANCELING(4121, "待发货-取消中"),
        DELIVERY_CANCELED(4122, "待发货已取消"),
        CANCEL_FAIL(4123, "待发货取消失败"),
        PARTIAL_SHIPMENT(4130, "待发货部分发货"),
        CONSIGNMENT_SHIPPING_TO_BE_SUBMITTED(4040, "代销发货待递交"),
        CONSIGNMENT_SHIPPING_SUBMITTED(4041, "代销发货已递交"),
        CANCELED(5010, "已取消"),
        CANCELED_AND_MERGED(5020, "已取消被合并"),
        CANCELED_AND_SPLIT(5030, "已取消被拆分"),
        DELIVERY_ON_THE_WAY(6000, "发货在途"),
        COMPLETED(9090, "已完成");


        private final Integer code;
        private final String desc;

        OrderDeliveryState(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static OrderDeliveryState getEnum(Integer code) {
            for (OrderDeliveryState orderDeliveryState : OrderDeliveryState.values()) {
                if (orderDeliveryState.getCode().equals(code)) {
                    return orderDeliveryState;
                }
            }
            throw new IllegalArgumentException("无效的吉客云状态");
        }
    }

    /**
     * 同步吉客云状态
     */
    public enum IsSync {
        /**
         * 未同步
         */
        NOT_SYNC(0, "未同步"),
        /**
         * 已同步
         */
        SYNC(1, "已同步");


        private final Integer code;
        private final String desc;

        IsSync(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static IsSync getEnum(Integer code) {
            for (IsSync isSync : IsSync.values()) {
                if (isSync.getCode().equals(code)) {
                    return isSync;
                }
            }
            throw new IllegalArgumentException("无效的同步吉客云");
        }
    }

    /**
     * 创建方式
     */
    public enum IsJkyCreate {
        /**
         * 分销系统
         */
        NOT_JKY(0, "分销系统"),
        /**
         * 吉客云
         */
        JKY(1, "吉客云");


        private final Integer code;
        private final String desc;

        IsJkyCreate(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static IsJkyCreate getEnum(Integer code) {
            for (IsJkyCreate isJkyCreate : IsJkyCreate.values()) {
                if (isJkyCreate.getCode().equals(code)) {
                    return isJkyCreate;
                }
            }
            throw new IllegalArgumentException("无效的创建方式");
        }
    }


}
