package net.jinyiyun.framework.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.jinyiyun.framework.dto.aftersale.ReimbursementRefundsDto;
import net.jinyiyun.framework.entity.ReimbursementRefunds;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【reimbursement_refunds(补偿退款单表)】的数据库操作Mapper
* @createDate 2024-06-26 17:22:26
* @Entity net.jinyiyun.framework.ReimbursementRefunds
*/
public interface ReimbursementRefundsMapper extends BaseMapper<ReimbursementRefunds> {


    /**
     * 根据发货单id集合查询补偿退款单及商品
     * @param deliveryIds
     * @return
     */
    List<ReimbursementRefundsDto> listReimbursementRefundsDtosByDeliveryIds(@Param("deliveryIds") List<Long> deliveryIds);
}
