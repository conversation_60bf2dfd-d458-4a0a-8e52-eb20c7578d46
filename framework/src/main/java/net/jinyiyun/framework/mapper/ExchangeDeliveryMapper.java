package net.jinyiyun.framework.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.jinyiyun.framework.dto.aftersale.ExchangeDeliveryDto;
import net.jinyiyun.framework.entity.ExchangeDelivery;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【exchange_delivery(退换补发表)】的数据库操作Mapper
* @createDate 2024-06-26 18:02:09
* @Entity net.jinyiyun.framework.entity.ExchangeDelivery
*/
public interface ExchangeDeliveryMapper extends BaseMapper<ExchangeDelivery> {



    /**
     * 获取已完成的售后单（可以开票）
     * @return
     */
    List<ExchangeDelivery> getFinishAfterSales(@Param("orderIds") Set<Long> orderIds);

    /**
     * 根据发货单id集合查询退换补发单及商品
     * @param deliveryIds
     * @return
     */
    List<ExchangeDeliveryDto> listExchangeDeliveryDtosByDeliveryIds(@Param("deliveryIds") List<Long> deliveryIds);
}
