package net.jinyiyun.framework.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.jinyiyun.framework.dto.aftersale.WrongMissDeliveryDto;
import net.jinyiyun.framework.entity.WrongMissDelivery;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【wrong_miss_delivery(错发漏发表)】的数据库操作Mapper
* @createDate 2024-06-26 18:45:21
* @Entity net.jinyiyun.framework.entity.WrongMissDelivery
*/
public interface WrongMissDeliveryMapper extends BaseMapper<WrongMissDelivery> {


    List<WrongMissDelivery> getTask();


    /**
     * 获取已完成的售后单（可以开票）
     * @return
     */
    List<WrongMissDelivery> getFinishAfterSales(@Param("orderIds") Set<Long> orderIds);


    /**
     * 根据发货单id集合查询错发漏发单及商品
     * @param deliveryIds
     * @return
     */
    List<WrongMissDeliveryDto> listWrongMissDeliveryDtosByDeliveryIds(@Param("deliveryIds") List<Long> deliveryIds);
}
